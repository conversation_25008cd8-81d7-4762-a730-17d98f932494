/**
 * @file PSRAM_USAGE_EXAMPLE.c
 * @brief PSRAM优化使用示例
 * 
 * 展示如何在HYP-Link项目中正确使用PSRAM优化功能
 */

#include <stdio.h>
#include <string.h>
#include <freertos/FreeRTOS.h>
#include <freertos/task.h>
#include <esp_heap_caps.h>
#include <esp_log.h>

#include "utils/memory_monitor.h"

static const char *TAG = "PSRAM_EXAMPLE";

/**
 * @brief 示例：智能内存分配
 */
void example_smart_memory_allocation(void) {
    ESP_LOGI(TAG, "=== Smart Memory Allocation Example ===");
    
    // 小型分配 - 自动使用内部RAM
    char *small_buffer = MALLOC_SMART(64);
    if (small_buffer) {
        strcpy(small_buffer, "Small buffer in internal RAM");
        ESP_LOGI(TAG, "Small buffer: %s", small_buffer);
        FREE_SMART(small_buffer);
    }
    
    // 大型分配 - 自动使用PSRAM
    char *large_buffer = MALLOC_SMART(8192);
    if (large_buffer) {
        snprintf(large_buffer, 8192, "Large buffer in PSRAM - size: %d bytes", 8192);
        ESP_LOGI(TAG, "Large buffer allocated successfully");
        ESP_LOGI(TAG, "Buffer location: %s", esp_ptr_external_ram(large_buffer) ? "PSRAM" : "Internal RAM");
        FREE_SMART(large_buffer);
    }
    
    // 强制PSRAM分配
    uint8_t *psram_buffer = MALLOC_PSRAM_FIRST(4096);
    if (psram_buffer) {
        memset(psram_buffer, 0xAA, 4096);
        ESP_LOGI(TAG, "PSRAM buffer allocated: %s", 
                 esp_ptr_external_ram(psram_buffer) ? "PSRAM" : "Internal RAM");
        heap_caps_free(psram_buffer);
    }
}

/**
 * @brief 示例任务函数
 */
void example_task_function(void *pvParameters) {
    const char *task_name = (const char *)pvParameters;
    
    ESP_LOGI(TAG, "Task %s started", task_name);
    
    // 在任务中使用智能内存分配
    for (int i = 0; i < 5; i++) {
        void *buffer = MALLOC_SMART(1024 + i * 512);
        if (buffer) {
            ESP_LOGI(TAG, "Task %s: Allocated %d bytes in %s", 
                     task_name, 1024 + i * 512,
                     esp_ptr_external_ram(buffer) ? "PSRAM" : "Internal RAM");
            
            // 模拟一些工作
            vTaskDelay(pdMS_TO_TICKS(1000));
            
            FREE_SMART(buffer);
        }
    }
    
    ESP_LOGI(TAG, "Task %s completed", task_name);
    vTaskDelete(NULL);
}

/**
 * @brief 示例：创建PSRAM栈任务
 */
void example_create_psram_tasks(void) {
    ESP_LOGI(TAG, "=== PSRAM Stack Task Creation Example ===");
    
    TaskHandle_t task1_handle = NULL;
    TaskHandle_t task2_handle = NULL;
    
    // 创建第一个任务，使用PSRAM栈
    BaseType_t result1 = CREATE_TASK_PSRAM_STACK(
        example_task_function,
        "PSRAMTask1",
        4096,
        "Task1",
        5,
        &task1_handle
    );
    
    if (result1 == pdPASS) {
        ESP_LOGI(TAG, "Task1 created successfully");
    } else {
        ESP_LOGE(TAG, "Failed to create Task1");
    }
    
    // 创建第二个任务，使用PSRAM栈
    BaseType_t result2 = CREATE_TASK_PSRAM_STACK(
        example_task_function,
        "PSRAMTask2",
        4096,
        "Task2",
        5,
        &task2_handle
    );
    
    if (result2 == pdPASS) {
        ESP_LOGI(TAG, "Task2 created successfully");
    } else {
        ESP_LOGE(TAG, "Failed to create Task2");
    }
}

/**
 * @brief 示例：内存监控使用
 */
void example_memory_monitoring(void) {
    ESP_LOGI(TAG, "=== Memory Monitoring Example ===");
    
    // 配置内存监控
    memory_monitor_config_t config = {
        .monitor_interval_ms = 2000,    // 2秒监控间隔
        .log_level = ESP_LOG_INFO,      // 信息级别日志
        .enable_leak_detection = true,  // 启用泄漏检测
        .alert_threshold_percent = 85   // 85%警告阈值
    };
    
    // 初始化并启动内存监控
    esp_err_t ret = memory_monitor_init(&config);
    if (ret == ESP_OK) {
        ESP_LOGI(TAG, "Memory monitor initialized successfully");
        
        ret = memory_monitor_start();
        if (ret == ESP_OK) {
            ESP_LOGI(TAG, "Memory monitoring started");
        } else {
            ESP_LOGE(TAG, "Failed to start memory monitoring");
        }
    } else {
        ESP_LOGE(TAG, "Failed to initialize memory monitor");
    }
    
    // 打印初始内存统计
    memory_monitor_print_stats();
}

/**
 * @brief 示例：传统vs优化的内存分配对比
 */
void example_memory_allocation_comparison(void) {
    ESP_LOGI(TAG, "=== Memory Allocation Comparison ===");
    
    // 传统方式 - 可能导致内部RAM不足
    ESP_LOGI(TAG, "Traditional allocation:");
    void *traditional_ptr = malloc(8192);
    if (traditional_ptr) {
        ESP_LOGI(TAG, "Traditional malloc: %s", 
                 esp_ptr_external_ram(traditional_ptr) ? "PSRAM" : "Internal RAM");
        free(traditional_ptr);
    }
    
    // 优化方式 - 智能选择内存类型
    ESP_LOGI(TAG, "Optimized allocation:");
    void *optimized_ptr = MALLOC_SMART(8192);
    if (optimized_ptr) {
        ESP_LOGI(TAG, "Smart malloc: %s", 
                 esp_ptr_external_ram(optimized_ptr) ? "PSRAM" : "Internal RAM");
        FREE_SMART(optimized_ptr);
    }
    
    // 强制PSRAM分配
    ESP_LOGI(TAG, "Forced PSRAM allocation:");
    void *psram_ptr = heap_caps_malloc(8192, MALLOC_CAP_SPIRAM | MALLOC_CAP_8BIT);
    if (psram_ptr) {
        ESP_LOGI(TAG, "Forced PSRAM malloc: %s", 
                 esp_ptr_external_ram(psram_ptr) ? "PSRAM" : "Internal RAM");
        heap_caps_free(psram_ptr);
    } else {
        ESP_LOGW(TAG, "PSRAM allocation failed, trying internal RAM");
        psram_ptr = heap_caps_malloc(8192, MALLOC_CAP_8BIT);
        if (psram_ptr) {
            ESP_LOGI(TAG, "Fallback to internal RAM successful");
            heap_caps_free(psram_ptr);
        }
    }
}

/**
 * @brief 示例：内存泄漏检测
 */
void example_memory_leak_detection(void) {
    ESP_LOGI(TAG, "=== Memory Leak Detection Example ===");
    
    // 模拟一些内存分配
    void *ptr1 = MALLOC_SMART(1024);
    void *ptr2 = MALLOC_SMART(2048);
    void *ptr3 = MALLOC_SMART(4096);
    
    // 只释放部分内存（模拟泄漏）
    FREE_SMART(ptr1);
    FREE_SMART(ptr2);
    // ptr3 未释放 - 这会被检测为潜在泄漏
    
    // 检查内存泄漏
    memory_monitor_check_leaks();
    
    // 清理剩余内存
    FREE_SMART(ptr3);
    
    ESP_LOGI(TAG, "After cleanup:");
    memory_monitor_check_leaks();
}

/**
 * @brief 主示例函数
 */
void psram_optimization_example(void) {
    ESP_LOGI(TAG, "Starting PSRAM optimization examples...");
    
    // 1. 初始化内存监控
    example_memory_monitoring();
    
    // 等待一下让监控启动
    vTaskDelay(pdMS_TO_TICKS(1000));
    
    // 2. 智能内存分配示例
    example_smart_memory_allocation();
    
    // 3. 内存分配对比示例
    example_memory_allocation_comparison();
    
    // 4. 创建PSRAM栈任务示例
    example_create_psram_tasks();
    
    // 5. 内存泄漏检测示例
    example_memory_leak_detection();
    
    // 等待任务完成
    vTaskDelay(pdMS_TO_TICKS(10000));
    
    // 最终内存统计
    ESP_LOGI(TAG, "Final memory statistics:");
    memory_monitor_print_stats();
    
    ESP_LOGI(TAG, "PSRAM optimization examples completed!");
}

/**
 * @brief 在app_main中调用此函数来运行示例
 */
void run_psram_examples(void) {
    // 创建示例任务
    xTaskCreate(
        (TaskFunction_t)psram_optimization_example,
        "PSRAMExample",
        8192,
        NULL,
        5,
        NULL
    );
}
