/**
 * @file memory_monitor.c
 * @brief PSRAM和内部RAM内存监控工具
 * 
 * 提供实时内存使用监控、内存分配统计和内存泄漏检测功能
 */

#include <stdio.h>
#include <string.h>
#include <freertos/FreeRTOS.h>
#include <freertos/task.h>
#include <freertos/timers.h>
#include <esp_heap_caps.h>
#include <esp_log.h>
#include <esp_timer.h>

#include "memory_monitor.h"

static const char *TAG = "MEM_MONITOR";

// 内存统计结构
typedef struct {
    size_t total_size;
    size_t free_size;
    size_t min_free_size;
    size_t largest_free_block;
    uint32_t allocation_count;
    uint32_t free_count;
    uint32_t peak_allocated;
} memory_stats_t;

// 全局内存统计
static memory_stats_t psram_stats = {0};
static memory_stats_t internal_stats = {0};
static bool monitoring_enabled = false;
static TimerHandle_t monitor_timer = NULL;

// 内存监控配置
static memory_monitor_config_t monitor_config = {
    .monitor_interval_ms = 5000,
    .log_level = ESP_LOG_INFO,
    .enable_leak_detection = true,
    .alert_threshold_percent = 90
};

/**
 * @brief 更新内存统计信息
 */
static void update_memory_stats(void) {
    // 更新PSRAM统计
    multi_heap_info_t psram_info;
    heap_caps_get_info(&psram_info, MALLOC_CAP_SPIRAM);
    
    psram_stats.total_size = psram_info.total_allocated_bytes + psram_info.total_free_bytes;
    psram_stats.free_size = psram_info.total_free_bytes;
    psram_stats.largest_free_block = psram_info.largest_free_block;
    
    if (psram_stats.min_free_size == 0 || psram_stats.free_size < psram_stats.min_free_size) {
        psram_stats.min_free_size = psram_stats.free_size;
    }
    
    size_t psram_allocated = psram_stats.total_size - psram_stats.free_size;
    if (psram_allocated > psram_stats.peak_allocated) {
        psram_stats.peak_allocated = psram_allocated;
    }
    
    // 更新内部RAM统计
    multi_heap_info_t internal_info;
    heap_caps_get_info(&internal_info, MALLOC_CAP_8BIT | MALLOC_CAP_INTERNAL);
    
    internal_stats.total_size = internal_info.total_allocated_bytes + internal_info.total_free_bytes;
    internal_stats.free_size = internal_info.total_free_bytes;
    internal_stats.largest_free_block = internal_info.largest_free_block;
    
    if (internal_stats.min_free_size == 0 || internal_stats.free_size < internal_stats.min_free_size) {
        internal_stats.min_free_size = internal_stats.free_size;
    }
    
    size_t internal_allocated = internal_stats.total_size - internal_stats.free_size;
    if (internal_allocated > internal_stats.peak_allocated) {
        internal_stats.peak_allocated = internal_allocated;
    }
}

/**
 * @brief 检查内存使用警告
 */
static void check_memory_alerts(void) {
    // 检查PSRAM使用率
    if (psram_stats.total_size > 0) {
        uint32_t psram_usage_percent = ((psram_stats.total_size - psram_stats.free_size) * 100) / psram_stats.total_size;
        if (psram_usage_percent >= monitor_config.alert_threshold_percent) {
            ESP_LOGW(TAG, "PSRAM usage high: %d%% (%d KB used, %d KB free)", 
                     psram_usage_percent, 
                     (psram_stats.total_size - psram_stats.free_size) / 1024,
                     psram_stats.free_size / 1024);
        }
    }
    
    // 检查内部RAM使用率
    if (internal_stats.total_size > 0) {
        uint32_t internal_usage_percent = ((internal_stats.total_size - internal_stats.free_size) * 100) / internal_stats.total_size;
        if (internal_usage_percent >= monitor_config.alert_threshold_percent) {
            ESP_LOGW(TAG, "Internal RAM usage high: %d%% (%d KB used, %d KB free)", 
                     internal_usage_percent,
                     (internal_stats.total_size - internal_stats.free_size) / 1024,
                     internal_stats.free_size / 1024);
        }
    }
}

/**
 * @brief 内存监控定时器回调
 */
static void memory_monitor_timer_callback(TimerHandle_t xTimer) {
    if (!monitoring_enabled) {
        return;
    }
    
    update_memory_stats();
    check_memory_alerts();
    
    if (monitor_config.log_level <= ESP_LOG_INFO) {
        memory_monitor_print_stats();
    }
}

/**
 * @brief 初始化内存监控
 */
esp_err_t memory_monitor_init(const memory_monitor_config_t *config) {
    if (config) {
        monitor_config = *config;
    }
    
    // 创建监控定时器
    monitor_timer = xTimerCreate(
        "MemMonitor",
        pdMS_TO_TICKS(monitor_config.monitor_interval_ms),
        pdTRUE,  // 自动重载
        NULL,
        memory_monitor_timer_callback
    );
    
    if (monitor_timer == NULL) {
        ESP_LOGE(TAG, "Failed to create memory monitor timer");
        return ESP_ERR_NO_MEM;
    }
    
    // 初始化统计数据
    update_memory_stats();
    
    ESP_LOGI(TAG, "Memory monitor initialized");
    ESP_LOGI(TAG, "PSRAM total: %d KB, Internal RAM total: %d KB", 
             psram_stats.total_size / 1024, internal_stats.total_size / 1024);
    
    return ESP_OK;
}

/**
 * @brief 启动内存监控
 */
esp_err_t memory_monitor_start(void) {
    if (monitor_timer == NULL) {
        ESP_LOGE(TAG, "Memory monitor not initialized");
        return ESP_ERR_INVALID_STATE;
    }
    
    monitoring_enabled = true;
    
    if (xTimerStart(monitor_timer, pdMS_TO_TICKS(1000)) != pdPASS) {
        ESP_LOGE(TAG, "Failed to start memory monitor timer");
        monitoring_enabled = false;
        return ESP_FAIL;
    }
    
    ESP_LOGI(TAG, "Memory monitoring started (interval: %d ms)", monitor_config.monitor_interval_ms);
    return ESP_OK;
}

/**
 * @brief 停止内存监控
 */
esp_err_t memory_monitor_stop(void) {
    if (monitor_timer == NULL) {
        return ESP_ERR_INVALID_STATE;
    }
    
    monitoring_enabled = false;
    
    if (xTimerStop(monitor_timer, pdMS_TO_TICKS(1000)) != pdPASS) {
        ESP_LOGW(TAG, "Failed to stop memory monitor timer");
    }
    
    ESP_LOGI(TAG, "Memory monitoring stopped");
    return ESP_OK;
}

/**
 * @brief 打印内存统计信息
 */
void memory_monitor_print_stats(void) {
    update_memory_stats();
    
    ESP_LOGI(TAG, "=== Memory Statistics ===");
    
    // PSRAM统计
    if (psram_stats.total_size > 0) {
        uint32_t psram_usage_percent = ((psram_stats.total_size - psram_stats.free_size) * 100) / psram_stats.total_size;
        ESP_LOGI(TAG, "PSRAM: %d%% used (%d/%d KB), Min free: %d KB, Largest block: %d KB",
                 psram_usage_percent,
                 (psram_stats.total_size - psram_stats.free_size) / 1024,
                 psram_stats.total_size / 1024,
                 psram_stats.min_free_size / 1024,
                 psram_stats.largest_free_block / 1024);
    } else {
        ESP_LOGI(TAG, "PSRAM: Not available");
    }
    
    // 内部RAM统计
    uint32_t internal_usage_percent = ((internal_stats.total_size - internal_stats.free_size) * 100) / internal_stats.total_size;
    ESP_LOGI(TAG, "Internal RAM: %d%% used (%d/%d KB), Min free: %d KB, Largest block: %d KB",
             internal_usage_percent,
             (internal_stats.total_size - internal_stats.free_size) / 1024,
             internal_stats.total_size / 1024,
             internal_stats.min_free_size / 1024,
             internal_stats.largest_free_block / 1024);
    
    // 任务栈使用情况
    ESP_LOGI(TAG, "Free heap size: %d KB", esp_get_free_heap_size() / 1024);
    ESP_LOGI(TAG, "Minimum free heap: %d KB", esp_get_minimum_free_heap_size() / 1024);
}

/**
 * @brief 获取PSRAM使用统计
 */
void memory_monitor_get_psram_stats(memory_stats_t *stats) {
    if (stats) {
        update_memory_stats();
        *stats = psram_stats;
    }
}

/**
 * @brief 获取内部RAM使用统计
 */
void memory_monitor_get_internal_stats(memory_stats_t *stats) {
    if (stats) {
        update_memory_stats();
        *stats = internal_stats;
    }
}

/**
 * @brief 智能内存分配函数 - 优先使用PSRAM
 */
void* memory_monitor_malloc_smart(size_t size) {
    void *ptr = NULL;
    
    // 对于大于32字节的分配，优先使用PSRAM
    if (size > 32 && psram_stats.free_size > size + 1024) {
        ptr = heap_caps_malloc(size, MALLOC_CAP_SPIRAM | MALLOC_CAP_8BIT);
        if (ptr) {
            psram_stats.allocation_count++;
            ESP_LOGD(TAG, "Allocated %d bytes in PSRAM", size);
            return ptr;
        }
    }
    
    // 回退到内部RAM
    ptr = heap_caps_malloc(size, MALLOC_CAP_8BIT);
    if (ptr) {
        internal_stats.allocation_count++;
        ESP_LOGD(TAG, "Allocated %d bytes in Internal RAM", size);
    } else {
        ESP_LOGE(TAG, "Failed to allocate %d bytes", size);
    }
    
    return ptr;
}

/**
 * @brief 智能内存释放函数
 */
void memory_monitor_free_smart(void *ptr) {
    if (!ptr) {
        return;
    }
    
    // 检查内存类型并更新统计
    if (heap_caps_get_allocated_size(ptr) > 0) {
        if (esp_ptr_external_ram(ptr)) {
            psram_stats.free_count++;
            ESP_LOGD(TAG, "Freed PSRAM allocation");
        } else {
            internal_stats.free_count++;
            ESP_LOGD(TAG, "Freed Internal RAM allocation");
        }
    }
    
    heap_caps_free(ptr);
}

/**
 * @brief 检查内存泄漏
 */
void memory_monitor_check_leaks(void) {
    ESP_LOGI(TAG, "=== Memory Leak Check ===");
    ESP_LOGI(TAG, "PSRAM: Allocations=%d, Frees=%d, Potential leaks=%d",
             psram_stats.allocation_count, psram_stats.free_count,
             psram_stats.allocation_count - psram_stats.free_count);
    ESP_LOGI(TAG, "Internal RAM: Allocations=%d, Frees=%d, Potential leaks=%d",
             internal_stats.allocation_count, internal_stats.free_count,
             internal_stats.allocation_count - internal_stats.free_count);
}
