/**
 * @file memory_monitor.h
 * @brief PSRAM和内部RAM内存监控工具头文件
 * 
 * 提供实时内存使用监控、内存分配统计和内存泄漏检测功能
 */

#ifndef MEMORY_MONITOR_H
#define MEMORY_MONITOR_H

#include <stdint.h>
#include <stddef.h>
#include <esp_err.h>
#include <esp_log.h>

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @brief 内存监控配置结构
 */
typedef struct {
    uint32_t monitor_interval_ms;      ///< 监控间隔（毫秒）
    esp_log_level_t log_level;         ///< 日志级别
    bool enable_leak_detection;        ///< 启用内存泄漏检测
    uint32_t alert_threshold_percent;  ///< 内存使用警告阈值（百分比）
} memory_monitor_config_t;

/**
 * @brief 内存统计结构
 */
typedef struct {
    size_t total_size;          ///< 总内存大小
    size_t free_size;           ///< 可用内存大小
    size_t min_free_size;       ///< 历史最小可用内存
    size_t largest_free_block;  ///< 最大连续可用块
    uint32_t allocation_count;  ///< 分配次数
    uint32_t free_count;        ///< 释放次数
    uint32_t peak_allocated;    ///< 峰值已分配内存
} memory_stats_t;

/**
 * @brief 默认内存监控配置
 */
#define MEMORY_MONITOR_DEFAULT_CONFIG() { \
    .monitor_interval_ms = 5000, \
    .log_level = ESP_LOG_INFO, \
    .enable_leak_detection = true, \
    .alert_threshold_percent = 90 \
}

/**
 * @brief 初始化内存监控
 * 
 * @param config 监控配置，NULL使用默认配置
 * @return ESP_OK 成功，其他值表示失败
 */
esp_err_t memory_monitor_init(const memory_monitor_config_t *config);

/**
 * @brief 启动内存监控
 * 
 * @return ESP_OK 成功，其他值表示失败
 */
esp_err_t memory_monitor_start(void);

/**
 * @brief 停止内存监控
 * 
 * @return ESP_OK 成功，其他值表示失败
 */
esp_err_t memory_monitor_stop(void);

/**
 * @brief 打印内存统计信息
 */
void memory_monitor_print_stats(void);

/**
 * @brief 获取PSRAM使用统计
 * 
 * @param stats 输出统计信息的结构指针
 */
void memory_monitor_get_psram_stats(memory_stats_t *stats);

/**
 * @brief 获取内部RAM使用统计
 * 
 * @param stats 输出统计信息的结构指针
 */
void memory_monitor_get_internal_stats(memory_stats_t *stats);

/**
 * @brief 智能内存分配函数 - 优先使用PSRAM
 * 
 * 根据分配大小和可用内存情况，智能选择使用PSRAM或内部RAM
 * 
 * @param size 要分配的内存大小
 * @return 分配的内存指针，失败返回NULL
 */
void* memory_monitor_malloc_smart(size_t size);

/**
 * @brief 智能内存释放函数
 * 
 * @param ptr 要释放的内存指针
 */
void memory_monitor_free_smart(void *ptr);

/**
 * @brief 检查内存泄漏
 * 
 * 打印内存分配和释放统计，帮助检测潜在的内存泄漏
 */
void memory_monitor_check_leaks(void);

/**
 * @brief 便利宏：智能内存分配
 */
#define MALLOC_SMART(size) memory_monitor_malloc_smart(size)

/**
 * @brief 便利宏：智能内存释放
 */
#define FREE_SMART(ptr) do { \
    memory_monitor_free_smart(ptr); \
    (ptr) = NULL; \
} while(0)

/**
 * @brief 便利宏：PSRAM优先分配
 */
#define MALLOC_PSRAM_FIRST(size) ({ \
    void *_ptr = heap_caps_malloc(size, MALLOC_CAP_SPIRAM | MALLOC_CAP_8BIT); \
    if (!_ptr) { \
        _ptr = heap_caps_malloc(size, MALLOC_CAP_8BIT); \
    } \
    _ptr; \
})

/**
 * @brief 便利宏：PSRAM优先calloc
 */
#define CALLOC_PSRAM_FIRST(count, size) ({ \
    void *_ptr = heap_caps_calloc(count, size, MALLOC_CAP_SPIRAM | MALLOC_CAP_8BIT); \
    if (!_ptr) { \
        _ptr = heap_caps_calloc(count, size, MALLOC_CAP_8BIT); \
    } \
    _ptr; \
})

/**
 * @brief 便利宏：创建PSRAM栈任务
 */
#define CREATE_TASK_PSRAM_STACK(task_func, name, stack_size, param, priority, handle) ({ \
    BaseType_t _result = xTaskCreateWithCaps(task_func, name, stack_size, param, priority, handle, MALLOC_CAP_SPIRAM); \
    if (_result != pdPASS) { \
        ESP_LOGW("TASK", "Failed to create task %s with PSRAM stack, trying internal RAM", name); \
        _result = xTaskCreate(task_func, name, stack_size, param, priority, handle); \
    } else { \
        ESP_LOGI("TASK", "Task %s created with PSRAM stack", name); \
    } \
    _result; \
})

#ifdef __cplusplus
}
#endif

#endif // MEMORY_MONITOR_H
