/**
 * @file network_reassembly_config.h
 * @brief Configuration for IPv6 reassembly and OpenThread buffer management
 * 
 * This file contains optimized settings to prevent IPv6 reassembly timeouts
 * in OpenThread mesh networks.
 */

#ifndef NETWORK_REASSEMBLY_CONFIG_H_
#define NETWORK_REASSEMBLY_CONFIG_H_

/* ========================================================================
 * IPv6 Reassembly Configuration
 * ======================================================================== */

/**
 * Enable IPv6 fragmentation and reassembly support
 */
#ifndef LWIP_IPV6_REASS
#define LWIP_IPV6_REASS 1
#endif

/**
 * Maximum time (in seconds) a fragmented IPv6 packet can be reassembled
 * Default is 60 seconds, we increase to 30 seconds for better reliability
 */
#ifndef IP_REASS_MAXAGE
#define IP_REASS_MAXAGE 30
#endif

/**
 * Maximum number of reassembly buffers
 * Increased from default 10 to handle more concurrent fragmentations
 */
#ifndef IP_REASS_MAX_PBUFS
#define IP_REASS_MAX_PBUFS 20
#endif

/**
 * Maximum datagram size for reassembly (bytes)
 */
#ifndef IP_REASS_MAX_DATAGRAM_SIZE
#define IP_REASS_MAX_DATAGRAM_SIZE 1500
#endif

/* ========================================================================
 * OpenThread Buffer Configuration
 * ======================================================================== */

/**
 * Minimum free buffers before triggering flow control
 */
#define OT_BUFFER_CRITICAL_THRESHOLD 15
#define OT_BUFFER_WARNING_THRESHOLD 20
#define OT_BUFFER_OPTIMAL_THRESHOLD 50

/**
 * Buffer monitoring intervals (milliseconds)
 */
#define OT_BUFFER_CHECK_INTERVAL_MS 100
#define OT_BUFFER_RECOVERY_DELAY_MS 10

/**
 * Maximum retry attempts for buffer allocation
 */
#define OT_BUFFER_RETRY_MAX 3

/* ========================================================================
 * CoAP Message Size Optimization
 * ======================================================================== */

/**
 * Maximum CoAP payload size to avoid fragmentation
 * Keep under IPv6 MTU (1280 bytes) minus headers
 */
#define COAP_MAX_PAYLOAD_SIZE 1024

/**
 * CoAP block size for large transfers
 */
#define COAP_BLOCK_SIZE_DEFAULT 512

/* ========================================================================
 * Network Quality Monitoring
 * ======================================================================== */

/**
 * RSSI threshold for network quality assessment
 */
#define NETWORK_RSSI_GOOD_THRESHOLD -40
#define NETWORK_RSSI_FAIR_THRESHOLD -60
#define NETWORK_RSSI_POOR_THRESHOLD -80

/**
 * Packet loss rate thresholds (percentage)
 */
#define NETWORK_LOSS_RATE_GOOD 1.0f
#define NETWORK_LOSS_RATE_FAIR 5.0f
#define NETWORK_LOSS_RATE_POOR 10.0f

/* ========================================================================
 * Adaptive Timeout Configuration
 * ======================================================================== */

/**
 * Base timeout values (milliseconds)
 */
#define REASSEMBLY_TIMEOUT_BASE_MS 5000
#define REASSEMBLY_TIMEOUT_MAX_MS 30000
#define REASSEMBLY_TIMEOUT_MIN_MS 1000

/**
 * Timeout adjustment factors based on network conditions
 */
#define TIMEOUT_FACTOR_GOOD_NETWORK 1.0f
#define TIMEOUT_FACTOR_FAIR_NETWORK 1.5f
#define TIMEOUT_FACTOR_POOR_NETWORK 2.0f

/* ========================================================================
 * Function Declarations
 * ======================================================================== */

/**
 * @brief Check OpenThread buffer availability
 * @return true if buffers are sufficient, false otherwise
 */
bool ot_check_buffer_availability(void);

/**
 * @brief Get adaptive timeout based on network conditions
 * @param base_timeout_ms Base timeout in milliseconds
 * @return Adjusted timeout in milliseconds
 */
uint32_t get_adaptive_reassembly_timeout(uint32_t base_timeout_ms);

/**
 * @brief Monitor and log network reassembly statistics
 */
void log_reassembly_statistics(void);

/**
 * @brief Initialize reassembly monitoring
 */
void init_reassembly_monitoring(void);

#endif /* NETWORK_REASSEMBLY_CONFIG_H_ */
