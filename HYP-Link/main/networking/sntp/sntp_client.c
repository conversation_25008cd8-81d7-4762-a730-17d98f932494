// Standard Library Headers
#include <time.h>
#include <sys/time.h>

// ESP-IDF Headers
#include "esp_netif_sntp.h"
#include "esp_sntp.h"
#include "esp_log.h"

// LWIP Headers
#include "lwip/ip_addr.h"

// FreeRTOS Headers
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"

#define TAG "sntp"

#define SLEEP_PERIOD_MS 3600000 // 1 hour in milliseconds

// SNTP服务器列表
static const char* sntp_servers[] = {
    "0.pool.ntp.org",
    "1.pool.ntp.org",
    "time.windows.com",
    "2.pool.ntp.org",
    "3.pool.ntp.org"
};

#define SNTP_SERVER_COUNT (sizeof(sntp_servers) / sizeof(sntp_servers[0]))
#define MAX_RETRIES_PER_SERVER 7  // 每个服务器最大重试次数

/**
 * @brief Synchronizes system time using SNTP with multiple servers and infinite retry.
 *
 * This function continuously attempts to synchronize time using multiple SNTP servers.
 * It tries each server up to MAX_RETRIES_PER_SERVER times before switching to the next server.
 * The process continues indefinitely until successful synchronization is achieved.
 *
 * @return Returns true if time synchronization is successful, false otherwise.
 */
bool synchronize_time(void)
{
    time_t now = 0;
    struct tm timeinfo = { 0 };
    int current_server_index = 0;
    int retry_count_for_current_server = 0;

    ESP_LOGI(TAG, "Starting SNTP time synchronization with %d servers", SNTP_SERVER_COUNT);

    // 无限重试直到成功
    while (true) {
        // 等待SNTP同步完成，每次尝试超时2000ms
        esp_err_t sync_result = esp_netif_sntp_sync_wait(2000 / portTICK_PERIOD_MS);

        if (sync_result == ESP_ERR_TIMEOUT) {
            retry_count_for_current_server++;
            ESP_LOGI(TAG, "Waiting for time sync from server '%s'... (attempt %d/%d)",
                     sntp_servers[current_server_index],
                     retry_count_for_current_server,
                     MAX_RETRIES_PER_SERVER);

            // 如果当前服务器重试次数达到上限，切换到下一个服务器
            if (retry_count_for_current_server >= MAX_RETRIES_PER_SERVER) {
                current_server_index = (current_server_index + 1) % SNTP_SERVER_COUNT;
                retry_count_for_current_server = 0;

                ESP_LOGW(TAG, "Switching to next SNTP server: %s", sntp_servers[current_server_index]);

                // 停止当前SNTP服务
                esp_netif_sntp_deinit();

                // 重新配置并启动SNTP服务，使用新的服务器
                esp_sntp_config_t config = ESP_NETIF_SNTP_DEFAULT_CONFIG(sntp_servers[current_server_index]);
                esp_netif_sntp_init(&config);
                esp_netif_sntp_start();

                // 短暂延迟后继续尝试
                vTaskDelay(pdMS_TO_TICKS(1000));
            }
            continue; // 继续重试
        } else {
            // 同步成功，跳出循环
            ESP_LOGI(TAG, "SNTP synchronization successful with server: %s", sntp_servers[current_server_index]);
            break;
        }
    }

    // 获取当前时间
    time(&now);

    // 格式化并打印当前时间
    char strftime_buf[64];
    setenv("TZ", "UTC", 1); // 设置时区为UTC
    tzset(); // 应用时区设置

    // 转换时间为本地时间结构
    localtime_r(&now, &timeinfo);

    // 格式化时间为字符串
    strftime(strftime_buf, sizeof(strftime_buf), "%c", &timeinfo);

    // 记录当前日期和时间
    ESP_LOGI(TAG, "The current date/time is: %s", strftime_buf);

    // 检查年份是否小于2023
    if (timeinfo.tm_year + 1900 < 2023) {
        ESP_LOGW(TAG, "The current date/time is not valid. Please check your network settings.");
        esp_restart();
        return false;
    }

    // 时间同步成功
    return true;
}

/**
 * @brief FreeRTOS task to periodically synchronize system time.
 *
 * This task sleeps for 24 hours, broken down into 1-hour intervals, 
 * and then calls the time synchronization function.
 *
 * @param pvParameters Task parameters (not used).
 */
void time_sync_task(void* pvParameters) {
    while (1) {
        ESP_LOGI(TAG, "Time sync task sleeping for 24 hours...");

        // Sleep for 24 hours in 1-hour intervals to avoid long blocking delays
        for (int i = 0; i < 24; i++) {
            vTaskDelay(pdMS_TO_TICKS(SLEEP_PERIOD_MS)); // Sleep for 1 hour, repeat 24 times
        }

        // Perform time synchronization after 24 hours
        synchronize_time();
    }
}

/**
 * @brief Creates and starts the SNTP time synchronization timer task.
 *
 * This function initializes SNTP with multiple servers and creates a FreeRTOS task
 * that synchronizes the time every 24 hours.
 *
 * @return Returns true on success, false on failure.
 */
bool sntp_client_init(void) {
    ESP_LOGI(TAG, "Initializing SNTP with %d servers", SNTP_SERVER_COUNT);

    // 使用第一个服务器初始化SNTP配置
    esp_sntp_config_t config = ESP_NETIF_SNTP_DEFAULT_CONFIG(sntp_servers[0]);
    esp_netif_sntp_init(&config);
    esp_netif_sntp_start();

    ESP_LOGI(TAG, "SNTP started with primary server: %s", sntp_servers[0]);

    // 创建时间同步任务，优先使用PSRAM栈
    BaseType_t result = xTaskCreateWithCaps(time_sync_task, "TimeSyncTask", 4096, NULL, 5, NULL, MALLOC_CAP_SPIRAM);
    if (result != pdPASS) {
        ESP_LOGW(TAG, "Failed to create time sync task with PSRAM stack, trying internal RAM");
        result = xTaskCreate(time_sync_task, "TimeSyncTask", 4096, NULL, 5, NULL);
    } else {
        ESP_LOGI(TAG, "Time sync task created with PSRAM stack");
    }

    if (result != pdPASS) {
        ESP_LOGE(TAG, "Failed to create time sync task.");
        return false;
    }

    // 立即执行一次时间同步
    return synchronize_time();
}
