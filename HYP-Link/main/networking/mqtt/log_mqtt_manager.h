#ifndef LOG_MQTT_MANAGER_H
#define LOG_MQTT_MANAGER_H

/* FreeRTOS 相关 */
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/semphr.h"
#include "freertos/event_groups.h"

/* coreMQTT 相关 */
#include "core_mqtt.h"
#include "core_mqtt_agent.h"

/* 网络传输相关 */
#include "transport_interface.h"
#include "network_transport.h"

/* 外部变量声明 */
extern NetworkContext_t xNetworkContext;

/**
 * @brief 日志MQTT管理器配置
 */
#define LOG_MQTT_AGENT_NETWORK_BUFFER_SIZE    (5120U)
#define LOG_MQTT_AGENT_COMMAND_QUEUE_LENGTH   (10U)
#define LOG_MQTT_AGENT_TASK_STACK_SIZE        (8192U)
#define LOG_MQTT_AGENT_TASK_PRIORITY          (tskIDLE_PRIORITY + 1)
#define LOG_CONNECTION_TASK_STACK_SIZE        (6144U)
#define LOG_CONNECTION_TASK_PRIORITY          (tskIDLE_PRIORITY + 2)

/**
 * @brief 日志MQTT连接事件位
 */
#define LOG_MQTT_AGENT_CONNECTED_BIT          (1UL << 0)
#define LOG_MQTT_AGENT_DISCONNECTED_BIT       (1UL << 1)

/**
 * @brief 启动日志MQTT管理器
 * 
 * @param[in] pxNetworkContextIn 网络上下文指针
 * @return pdPASS 成功，pdFAIL 失败
 */
BaseType_t xLogMqttManagerStart(NetworkContext_t *pxNetworkContextIn);

/**
 * @brief 停止日志MQTT管理器
 * 
 * @return pdPASS 成功，pdFAIL 失败
 */
BaseType_t xLogMqttManagerStop(void);

/**
 * @brief 发布日志到MQTT主题
 *
 * @param[in] pTopicFilter 主题过滤器
 * @param[in] topicFilterLength 主题长度
 * @param[in] pPayload 负载数据
 * @param[in] payloadLength 负载长度
 * @return pdPASS 成功，pdFAIL 失败
 */
BaseType_t xLogMqttPublish(const char *pTopicFilter,
                           uint16_t topicFilterLength,
                           const char *pPayload,
                           size_t payloadLength);

/**
 * @brief 发布日志到MQTT主题（带回调函数）
 *
 * @param[in] pTopicFilter 主题过滤器
 * @param[in] topicFilterLength 主题长度
 * @param[in] pPayload 负载数据
 * @param[in] payloadLength 负载长度
 * @param[in] pCallback 完成回调函数
 * @param[in] pCallbackContext 回调上下文
 * @return pdPASS 成功，pdFAIL 失败
 */
BaseType_t xLogMqttPublishWithCallback(const char *pTopicFilter,
                                       uint16_t topicFilterLength,
                                       const char *pPayload,
                                       size_t payloadLength,
                                       MQTTAgentCommandCallback_t pCallback,
                                       void *pCallbackContext);

/**
 * @brief 检查日志MQTT连接状态
 * 
 * @return pdTRUE 已连接，pdFALSE 未连接
 */
BaseType_t xLogMqttIsConnected(void);

/**
 * @brief 获取日志MQTT Agent上下文
 *
 * @return MQTTAgentContext_t* 日志MQTT Agent上下文指针
 */
MQTTAgentContext_t *pxGetLogMqttAgentContext(void);

/**
 * @brief 初始化日志服务器
 */
void log_server_init(void);

#endif /* LOG_MQTT_MANAGER_H */
