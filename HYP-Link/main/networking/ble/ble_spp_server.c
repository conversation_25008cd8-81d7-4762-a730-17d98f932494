/*
 * SPDX-FileCopyrightText: 2021-2022 Espressif Systems (Shanghai) CO LTD
 * Adapted for HYP-Link by AI Assistant
 *
 * SPDX-License-Identifier: Unlicense OR CC0-1.0
 */

#include "networking/ble/ble_spp_server.h"

#include <stdbool.h>
#include <stdint.h>
#include <string.h>
#include <stdio.h>

#include "esp_log.h"
#include "esp_system.h"
#include "nvs_flash.h"
#include "nvs.h"

#include "freertos/FreeRTOS.h"
#include "freertos/task.h"

#include "nimble/nimble_port.h"
#include "nimble/nimble_port_freertos.h"
#include "host/ble_hs.h"
#include "host/util/util.h"
#include "services/gap/ble_svc_gap.h"
#include "services/gatt/ble_svc_gatt.h"
#include "console/console.h" // For print_addr
#include "networking/wifi/app_wifi.h"

#include "core/app_manager.h" // For ledtask_operation

// Define NimBLE component configuration options if not done globally
// These might be needed depending on sdkconfig
#ifndef CONFIG_BT_NIMBLE_MAX_CONNECTIONS
#define CONFIG_BT_NIMBLE_MAX_CONNECTIONS 1
#endif
#ifndef CONFIG_EXAMPLE_IO_TYPE
#define CONFIG_EXAMPLE_IO_TYPE BLE_HS_IO_NO_INPUT_OUTPUT // Example default
#endif

static const char *TAG = "BLE_SPP_SERVER";

// NVS Constants
static const char *NVS_NAMESPACE = "settings";
static const char *CONFIG_PRESENT_KEY = "configured";
static const char *WIFI_SSID_KEY = "wifi_ssid";
static const char *WIFI_PASS_KEY = "wifi_pass";
static const char *WIFI_ENABLED_KEY = "wifi_en";
static const char *ETH_ENABLED_KEY = "eth_en";
static const char *CELL_ENABLED_KEY = "cell_en";

static uint8_t own_addr_type;
static uint16_t ble_spp_svc_gatt_read_val_handle;
static bool conn_handle_subs[CONFIG_BT_NIMBLE_MAX_CONNECTIONS + 1];
static bool is_connected = false;
static uint16_t current_conn_handle = BLE_HS_CONN_HANDLE_NONE;

// Forward declarations
static int ble_spp_server_gap_event(struct ble_gap_event *event, void *arg);
static int ble_svc_gatt_handler(uint16_t conn_handle, uint16_t attr_handle, struct ble_gatt_access_ctxt *ctxt, void *arg);
static void ble_spp_server_advertise(void);

/**
 * Logs information about a connection to the console.
 */
static void ble_spp_server_print_conn_desc(struct ble_gap_conn_desc *desc)
{
    ESP_LOGI(TAG, "handle=%d our_ota_addr_type=%d our_ota_addr=",
                desc->conn_handle, desc->our_ota_addr.type);
    print_addr(desc->our_ota_addr.val);
    ESP_LOGI(TAG, " our_id_addr_type=%d our_id_addr=",
                desc->our_id_addr.type);
    print_addr(desc->our_id_addr.val);
    ESP_LOGI(TAG, " peer_ota_addr_type=%d peer_ota_addr=",
                desc->peer_ota_addr.type);
    print_addr(desc->peer_ota_addr.val);
    ESP_LOGI(TAG, " peer_id_addr_type=%d peer_id_addr=",
                desc->peer_id_addr.type);
    print_addr(desc->peer_id_addr.val);
    ESP_LOGI(TAG, " conn_itvl=%d conn_latency=%d supervision_timeout=%d "
                "encrypted=%d authenticated=%d bonded=%d",
                desc->conn_itvl, desc->conn_latency,
                desc->supervision_timeout,
                desc->sec_state.encrypted,
                desc->sec_state.authenticated,
                desc->sec_state.bonded);
}

/**
 * Enables advertising with the following parameters:
 *     o General discoverable mode.
 *     o Undirected connectable mode.
 */
static void ble_spp_server_advertise(void)
{
    struct ble_gap_adv_params adv_params;
    struct ble_hs_adv_fields fields;
    const char *name;
    int rc;

    memset(&fields, 0, sizeof fields);

    fields.flags = BLE_HS_ADV_F_DISC_GEN | BLE_HS_ADV_F_BREDR_UNSUP;
    fields.tx_pwr_lvl_is_present = 1;
    fields.tx_pwr_lvl = BLE_HS_ADV_TX_PWR_LVL_AUTO;

    name = ble_svc_gap_device_name();
    fields.name = (uint8_t *)name;
    fields.name_len = strlen(name);
    fields.name_is_complete = 1;

    fields.uuids16 = (ble_uuid16_t[]) {
        BLE_UUID16_INIT(BLE_SVC_SPP_UUID16)
    };
    fields.num_uuids16 = 1;
    fields.uuids16_is_complete = 1;

    rc = ble_gap_adv_set_fields(&fields);
    if (rc != 0) {
        ESP_LOGE(TAG, "error setting advertisement data; rc=%d", rc);
        return;
    }

    memset(&adv_params, 0, sizeof adv_params);
    adv_params.conn_mode = BLE_GAP_CONN_MODE_UND;
    adv_params.disc_mode = BLE_GAP_DISC_MODE_GEN;
    rc = ble_gap_adv_start(own_addr_type, NULL, BLE_HS_FOREVER,
                           &adv_params, ble_spp_server_gap_event, NULL);
    if (rc != 0) {
        ESP_LOGE(TAG, "error enabling advertisement; rc=%d", rc);
        return;
    }
    ESP_LOGI(TAG, "BLE advertising started");
}

static esp_err_t save_config_flag_and_restart()
{
    nvs_handle_t nvs_handle;
    esp_err_t err;

    ESP_LOGI(TAG, "Saving configuration flag and restarting...");

    err = nvs_open(NVS_NAMESPACE, NVS_READWRITE, &nvs_handle);
    if (err != ESP_OK) {
        ESP_LOGE(TAG, "Error (%s) opening NVS handle!", esp_err_to_name(err));
        return err;
    }

    err = nvs_set_u8(nvs_handle, CONFIG_PRESENT_KEY, 1);
    if (err != ESP_OK) {
        ESP_LOGE(TAG, "Error (%s) setting configured flag in NVS!", esp_err_to_name(err));
        nvs_close(nvs_handle);
        return err;
    }

    err = nvs_commit(nvs_handle);
    if (err != ESP_OK) {
        ESP_LOGE(TAG, "Error (%s) committing NVS changes!", esp_err_to_name(err));
        nvs_close(nvs_handle);
        return err;
    }

    nvs_close(nvs_handle);
    ESP_LOGI(TAG, "Configuration saved. Restarting device.");

    // Optional: Send a confirmation back to the client before restarting
    // struct os_mbuf *txom = ble_hs_mbuf_from_str("OK\r\n");
    // if (txom && current_conn_handle != BLE_HS_CONN_HANDLE_NONE) {
    //    ble_gatts_notify_custom(current_conn_handle, ble_spp_svc_gatt_read_val_handle, txom);
    // }
    // vTaskDelay(pdMS_TO_TICKS(100)); // Short delay to allow notification sending

    esp_restart();
    return ESP_OK; // Will not be reached
}

/* Callback function for custom service */
static int ble_svc_gatt_handler(uint16_t conn_handle, uint16_t attr_handle, struct ble_gatt_access_ctxt *ctxt, void *arg)
{
    esp_err_t err;
    nvs_handle_t nvs_handle;

    switch (ctxt->op) {
    case BLE_GATT_ACCESS_OP_READ_CHR:
        ESP_LOGI(TAG, "Callback for read - Not implemented for SPP write characteristic");
        // Normally, you might read stored config values here if needed.
        return 0; // Return 0 for success

    case BLE_GATT_ACCESS_OP_WRITE_CHR:
        ESP_LOGI(TAG, "Data received in write event, conn_handle = %d, attr_handle = %d", conn_handle, attr_handle);

        uint16_t data_len = OS_MBUF_PKTLEN(ctxt->om);
        if (data_len == 0) {
            ESP_LOGW(TAG, "Received empty write request");
            return BLE_ATT_ERR_INVALID_ATTR_VALUE_LEN;
        }

        // Allocate buffer for data + null terminator using PSRAM first
        char *data = heap_caps_malloc(data_len + 1, MALLOC_CAP_SPIRAM | MALLOC_CAP_8BIT);
        if (!data) {
            // Fallback to internal RAM if PSRAM allocation fails
            data = heap_caps_malloc(data_len + 1, MALLOC_CAP_8BIT);
        }
        if (!data) {
            ESP_LOGE(TAG, "Failed to allocate memory for received data");
            return BLE_ATT_ERR_INSUFFICIENT_RES;
        }

        // Copy data from mbuf
        int rc = ble_hs_mbuf_to_flat(ctxt->om, data, data_len, NULL);
        if (rc != 0) {
            ESP_LOGE(TAG, "Failed to copy data from mbuf, rc=%d", rc);
            heap_caps_free(data);
            return BLE_ATT_ERR_UNLIKELY;
        }
        data[data_len] = '\0'; // Null-terminate the string

        ESP_LOGI(TAG, "Received data: %s", data);

        // --- Parse and Process Commands --- 
        if (strncmp(data, "WIFI:", 5) == 0) {
            char ssid[64] = {0};
            char pass[64] = {0};
            // Expected format: WIFI:ssid,password
            int scan_res = sscanf(data + 5, "%63[^,],%63s", ssid, pass);
            if (scan_res == 2) {
                ESP_LOGI(TAG, "Parsed WIFI - SSID: '%s', Pass: '%s'", ssid, pass);
                err = app_wifi_connect_manually(ssid, pass);
                if (err != ESP_OK) {
                    ESP_LOGE(TAG, "Failed to save Wi-Fi credentials: %s", esp_err_to_name(err));
                    // Optionally notify client about failure
                } else {
                    ESP_LOGI(TAG, "Wi-Fi credentials queued for saving.");
                    
                    // Set wifi_en flag in NVS
                    err = nvs_open(NVS_NAMESPACE, NVS_READWRITE, &nvs_handle);
                    if (err == ESP_OK) {
                        err = nvs_set_u8(nvs_handle, WIFI_ENABLED_KEY, 1);
                        if (err == ESP_OK) err = nvs_commit(nvs_handle);
                        if (err != ESP_OK) ESP_LOGE(TAG, "NVS WiFi enable flag save error: %s", esp_err_to_name(err));
                        nvs_close(nvs_handle);
                    } else {
                        ESP_LOGE(TAG, "NVS open error: %s", esp_err_to_name(err));
                    }
                }
            } else {
                ESP_LOGW(TAG, "Failed to parse WIFI command format. Expected WIFI:ssid,password");
            }
        } else if (strncmp(data, "ETH:", 4) == 0) {
            int eth_enabled = -1;
            // Expected format: ETH:0 or ETH:1
            if (sscanf(data + 4, "%d", &eth_enabled) == 1 && (eth_enabled == 0 || eth_enabled == 1)) {
                ESP_LOGI(TAG, "Parsed ETH - Enabled: %d", eth_enabled);
                err = nvs_open(NVS_NAMESPACE, NVS_READWRITE, &nvs_handle);
                if (err == ESP_OK) {
                    err = nvs_set_u8(nvs_handle, ETH_ENABLED_KEY, (uint8_t)eth_enabled);
                    if (err == ESP_OK) err = nvs_commit(nvs_handle);
                    if (err != ESP_OK) ESP_LOGE(TAG, "NVS ETH save error: %s", esp_err_to_name(err));
                    nvs_close(nvs_handle);
                } else {
                    ESP_LOGE(TAG, "NVS open error: %s", esp_err_to_name(err));
                }
            } else {
                ESP_LOGW(TAG, "Failed to parse ETH command format. Expected ETH:0 or ETH:1");
            }
        } else if (strncmp(data, "4G:", 3) == 0) {
            int cell_enabled = -1;
            // Expected format: 4G:0 or 4G:1
            if (sscanf(data + 3, "%d", &cell_enabled) == 1 && (cell_enabled == 0 || cell_enabled == 1)) {
                 ESP_LOGI(TAG, "Parsed 4G - Enabled: %d", cell_enabled);
                 err = nvs_open(NVS_NAMESPACE, NVS_READWRITE, &nvs_handle);
                 if (err == ESP_OK) {
                     err = nvs_set_u8(nvs_handle, CELL_ENABLED_KEY, (uint8_t)cell_enabled);
                     if (err == ESP_OK) err = nvs_commit(nvs_handle);
                     if (err != ESP_OK) ESP_LOGE(TAG, "NVS 4G save error: %s", esp_err_to_name(err));
                     nvs_close(nvs_handle);
                 } else {
                    ESP_LOGE(TAG, "NVS open error: %s", esp_err_to_name(err));
                 }
            } else {
                ESP_LOGW(TAG, "Failed to parse 4G command format. Expected 4G:0 or 4G:1");
            }
        } else if (strcmp(data, "SAVE") == 0) {
             ESP_LOGI(TAG, "Received SAVE command.");
             save_config_flag_and_restart();
             // Restart happens inside the function
        } else {
            ESP_LOGW(TAG, "Received unknown command: %s", data);
            // Optionally send back an error message
        }

        heap_caps_free(data);
        return 0; // Return 0 on successful handling

    default:
        ESP_LOGI(TAG, "Default Callback op=%d", ctxt->op);
        // Indicate that the request is not supported for other operations.
        return BLE_ATT_ERR_REQ_NOT_SUPPORTED;
    }
}

/* Define the SPP Service */
static const struct ble_gatt_svc_def ble_spp_gatt_svcs[] = {
    {
        .type = BLE_GATT_SVC_TYPE_PRIMARY,
        .uuid = BLE_UUID16_DECLARE(BLE_SVC_SPP_UUID16),
        .characteristics = (struct ble_gatt_chr_def[])
        { { // SPP Data Characteristic
                .uuid = BLE_UUID16_DECLARE(BLE_SVC_SPP_CHR_UUID16),
                .access_cb = ble_svc_gatt_handler,
                .val_handle = &ble_spp_svc_gatt_read_val_handle, // Store characteristic value handle
                .flags = BLE_GATT_CHR_F_READ | BLE_GATT_CHR_F_WRITE | BLE_GATT_CHR_F_NOTIFY,
            }, {
                0, /* No more characteristics */
            }
        },
    },
    {
        0, /* No more services. */
    },
};

/* GATT server registration callback */
static void gatt_svr_register_cb(struct ble_gatt_register_ctxt *ctxt, void *arg)
{
    char buf[BLE_UUID_STR_LEN];

    switch (ctxt->op) {
    case BLE_GATT_REGISTER_OP_SVC:
        ESP_LOGD(TAG, "registered service %s with handle=%d",
                    ble_uuid_to_str(ctxt->svc.svc_def->uuid, buf),
                    ctxt->svc.handle);
        break;

    case BLE_GATT_REGISTER_OP_CHR:
        ESP_LOGD(TAG, "registering characteristic %s with def_handle=%d val_handle=%d",
                    ble_uuid_to_str(ctxt->chr.chr_def->uuid, buf),
                    ctxt->chr.def_handle,
                    ctxt->chr.val_handle);
        break;

    case BLE_GATT_REGISTER_OP_DSC:
        ESP_LOGD(TAG, "registering descriptor %s with handle=%d",
                    ble_uuid_to_str(ctxt->dsc.dsc_def->uuid, buf),
                    ctxt->dsc.handle);
        break;

    default:
        assert(0);
        break;
    }
}

/* Initialize GATT services */
static esp_err_t gatt_svr_init_func(void)
{
    int rc;

    ble_svc_gap_init();
    ble_svc_gatt_init();

    rc = ble_gatts_count_cfg(ble_spp_gatt_svcs);
    if (rc != 0) {
        ESP_LOGE(TAG, "ble_gatts_count_cfg failed: %d", rc);
        return ESP_FAIL;
    }

    rc = ble_gatts_add_svcs(ble_spp_gatt_svcs);
    if (rc != 0) {
        ESP_LOGE(TAG, "ble_gatts_add_svcs failed: %d", rc);
        return ESP_FAIL;
    }

    ESP_LOGI(TAG, "GATT server initialized successfully.");
    return ESP_OK;
}

/**
 * The nimble host executes this callback when a GAP event occurs.
 */
static int ble_spp_server_gap_event(struct ble_gap_event *event, void *arg)
{
    struct ble_gap_conn_desc desc;
    int rc;

    switch (event->type) {
    case BLE_GAP_EVENT_CONNECT:
        ESP_LOGI(TAG, "connection %s; status=%d",
                    event->connect.status == 0 ? "established" : "failed",
                    event->connect.status);
        if (event->connect.status == 0) {
            rc = ble_gap_conn_find(event->connect.conn_handle, &desc);
            assert(rc == 0);
            ble_spp_server_print_conn_desc(&desc);
            is_connected = true;
            current_conn_handle = event->connect.conn_handle;
            ledtask_operation(BLUE, SLOW); // Blue slow blink or steady blue when connected
        } else {
            /* Connection failed; resume advertising */
            ble_spp_server_advertise();
        }
        return 0;

    case BLE_GAP_EVENT_DISCONNECT:
        ESP_LOGI(TAG, "disconnect; reason=%d", event->disconnect.reason);
        ble_spp_server_print_conn_desc(&event->disconnect.conn);
        is_connected = false;
        current_conn_handle = BLE_HS_CONN_HANDLE_NONE;
        // Clear subscription state for this handle
        if (event->disconnect.conn.conn_handle < (sizeof(conn_handle_subs) / sizeof(conn_handle_subs[0]))) {
             conn_handle_subs[event->disconnect.conn.conn_handle] = false;
        }

        ledtask_operation(BLUE, FAST); // Blue fast blink when disconnected/advertising

        /* Connection terminated; resume advertising */
        ble_spp_server_advertise();
        return 0;

    case BLE_GAP_EVENT_CONN_UPDATE:
        ESP_LOGI(TAG, "connection updated; status=%d", event->conn_update.status);
        rc = ble_gap_conn_find(event->conn_update.conn_handle, &desc);
        assert(rc == 0);
        ble_spp_server_print_conn_desc(&desc);
        return 0;

    case BLE_GAP_EVENT_ADV_COMPLETE:
        ESP_LOGI(TAG, "advertise complete; reason=%d", event->adv_complete.reason);
        // Only restart advertising if not connected
        if (!is_connected) {
             ble_spp_server_advertise();
        }
        return 0;

    case BLE_GAP_EVENT_MTU:
        ESP_LOGI(TAG, "mtu update event; conn_handle=%d cid=%d mtu=%d",
                    event->mtu.conn_handle,
                    event->mtu.channel_id,
                    event->mtu.value);
        return 0;

    case BLE_GAP_EVENT_SUBSCRIBE:
        ESP_LOGI(TAG, "subscribe event; conn_handle=%d attr_handle=%d reason=%d prevn=%d curn=%d previ=%d curi=%d",
                    event->subscribe.conn_handle,
                    event->subscribe.attr_handle,
                    event->subscribe.reason,
                    event->subscribe.prev_notify,
                    event->subscribe.cur_notify,
                    event->subscribe.prev_indicate,
                    event->subscribe.cur_indicate);
        // Check if the subscription is for our SPP characteristic
        if (event->subscribe.attr_handle == ble_spp_svc_gatt_read_val_handle) {
             if (event->subscribe.conn_handle < (sizeof(conn_handle_subs) / sizeof(conn_handle_subs[0]))) {
                 conn_handle_subs[event->subscribe.conn_handle] = (event->subscribe.cur_notify || event->subscribe.cur_indicate);
                 ESP_LOGI(TAG, "Subscription status for handle %d: %s",
                     event->subscribe.conn_handle, conn_handle_subs[event->subscribe.conn_handle] ? "Enabled" : "Disabled");
             } else {
                 ESP_LOGW(TAG, "Connection handle %d out of bounds for subscription array", event->subscribe.conn_handle);
             }
        }
        return 0;

    default:
        ESP_LOGD(TAG, "Unhandled GAP event: %d", event->type);
        return 0;
    }
}

/* Called when the host resets */
static void ble_spp_server_on_reset(int reason)
{
    ESP_LOGE(TAG, "Resetting state; reason=%d", reason);
}

/* Called when the host synchronizes */
static void ble_spp_server_on_sync(void)
{
    int rc;

    rc = ble_hs_util_ensure_addr(0);
    assert(rc == 0);

    /* Figure out address to use while advertising (no privacy for now) */
    rc = ble_hs_id_infer_auto(0, &own_addr_type);
    if (rc != 0) {
        ESP_LOGE(TAG, "error determining address type; rc=%d", rc);
        return;
    }

    /* Print the device address */
    uint8_t addr_val[6] = {0};
    rc = ble_hs_id_copy_addr(own_addr_type, addr_val, NULL);
    assert(rc == 0);
    ESP_LOGI(TAG, "Device Address: %02x:%02x:%02x:%02x:%02x:%02x",
             addr_val[5], addr_val[4], addr_val[3], addr_val[2], addr_val[1], addr_val[0]);

    /* Begin advertising */
    ble_spp_server_advertise();
}

/* NimBLE host task */
void ble_spp_server_host_task(void *param)
{
    ESP_LOGI(TAG, "BLE Host Task Started");
    /* This function will return only when nimble_port_stop() is executed */
    nimble_port_run();

    /* Clean up */
    nimble_port_freertos_deinit();
    ESP_LOGI(TAG, "BLE Host Task Ended");
}

// --- Public API Functions --- //

esp_err_t ble_spp_server_init(void)
{
    int rc;

    // Initialize connection handle subscription array
    memset(conn_handle_subs, 0, sizeof(conn_handle_subs));

    /* Initialize the NimBLE host configuration. */
    ble_hs_cfg.reset_cb = ble_spp_server_on_reset;
    ble_hs_cfg.sync_cb = ble_spp_server_on_sync;
    ble_hs_cfg.gatts_register_cb = gatt_svr_register_cb;
    ble_hs_cfg.store_status_cb = ble_store_util_status_rr; // Provided by NimBLE store utility

    // Configure Security Manager parameters
    ble_hs_cfg.sm_io_cap = CONFIG_EXAMPLE_IO_TYPE; // Use default or sdkconfig value
#ifdef CONFIG_EXAMPLE_BONDING
    ble_hs_cfg.sm_bonding = 1;
#else
    ble_hs_cfg.sm_bonding = 0; // Bonding might be useful
#endif
#ifdef CONFIG_EXAMPLE_MITM
    ble_hs_cfg.sm_mitm = 1;
#else
    ble_hs_cfg.sm_mitm = 0;
#endif
#ifdef CONFIG_EXAMPLE_USE_SC
    ble_hs_cfg.sm_sc = 1; // Secure Connections
#else
    ble_hs_cfg.sm_sc = 0;
#endif
#ifdef CONFIG_EXAMPLE_BONDING
    ble_hs_cfg.sm_our_key_dist = BLE_SM_PAIR_KEY_DIST_ENC | BLE_SM_PAIR_KEY_DIST_ID; // Distribute keys if bonding
    ble_hs_cfg.sm_their_key_dist = BLE_SM_PAIR_KEY_DIST_ENC | BLE_SM_PAIR_KEY_DIST_ID;
#else
    ble_hs_cfg.sm_our_key_dist = 0;
    ble_hs_cfg.sm_their_key_dist = 0;
#endif

    /* Initialize the GATT server */
    rc = gatt_svr_init_func();
    if (rc != ESP_OK) {
        ESP_LOGE(TAG, "gatt_svr_init_func failed");
        return rc;
    }

    /* Set the default device name. */
    rc = ble_svc_gap_device_name_set("HYP-Link-Config");
    if (rc != 0) {
        ESP_LOGE(TAG, "ble_svc_gap_device_name_set failed: %d", rc);
        return ESP_FAIL;
    }

    /* ble_store_config_init() is called externally before checking config status */

    ESP_LOGI(TAG, "BLE SPP Server Initialized");
    return ESP_OK;
}

void ble_spp_server_start(void)
{
    nimble_port_freertos_init(ble_spp_server_host_task);
    // Host task starts advertising via the sync_cb
}

bool is_ble_connected(void)
{
    return is_connected;
}

// Note: ble_store_config_init() function needs to be linked correctly.
// It's often part of the NimBLE porting layer or examples.
// If missing, you might need to copy its implementation from an ESP-IDF NimBLE example,
// ensuring it correctly uses NVS for storage. 