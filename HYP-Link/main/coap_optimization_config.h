/*
 * CoAP Optimization Configuration for HYP-Link
 * 
 * This file contains optimized configuration values for libcoap and OpenThread
 * to improve performance and reduce memory usage while maximizing throughput.
 */

#ifndef COAP_OPTIMIZATION_CONFIG_H_
#define COAP_OPTIMIZATION_CONFIG_H_

#include "sdkconfig.h"

/* ========================================================================
 * LibCoAP Memory and Session Optimization
 * ======================================================================== */

/* Increase maximum sessions for better concurrent handling */
#ifndef COAP_MAX_SESSIONS
#define COAP_MAX_SESSIONS 160  /* Further reduced to save memory */
#endif

/* Increase PDU limits for better throughput */
#ifndef COAP_MAX_PDUS
#define COAP_MAX_PDUS 64  /* Further reduced to save memory */
#endif

/* Increase packet buffer limits */
#ifndef COAP_MAX_PACKETS
#define COAP_MAX_PACKETS 64  /* Further reduced to save memory */
#endif

/* Increase node limits for retransmission queue */
#ifndef COAP_MAX_NODES
#define COAP_MAX_NODES 64  /* Further reduced to save memory */
#endif

/* Increase string buffer limits */
#ifndef COAP_MAX_STRINGS
#define COAP_MAX_STRINGS 48  /* Reduced from 32 to save memory */
#endif

/* Increase option limits */
#ifndef COAP_MAX_OPTIONS
#define COAP_MAX_OPTIONS 48  /* Reduced from 32 to save memory */
#endif

/* Increase DTLS session limits */
#ifndef COAP_MAX_DTLS_SESSIONS
#define COAP_MAX_DTLS_SESSIONS 6  /* Reduced from 8 to save memory */
#endif

/* Increase endpoint limits */
#ifndef COAP_MAX_ENDPOINTS
#define COAP_MAX_ENDPOINTS 6  /* Reduced from 8 to save memory */
#endif

/* ========================================================================
 * OpenThread Buffer Optimization
 * ======================================================================== */

/* These are typically set in OpenThread configuration, but we define
 * them here for reference and potential override */

/* Increase message buffer pool size */
#ifndef OPENTHREAD_CONFIG_MESSAGE_BUFFER_SIZE
#define OPENTHREAD_CONFIG_MESSAGE_BUFFER_SIZE 256
#endif

/* Increase number of message buffers */
#ifndef OPENTHREAD_CONFIG_NUM_MESSAGE_BUFFERS
#define OPENTHREAD_CONFIG_NUM_MESSAGE_BUFFERS 512  /* Further increased for better reassembly */
#endif

/* IPv6 Reassembly Configuration */
#ifndef OPENTHREAD_CONFIG_IP6_FRAGMENTATION_ENABLE
#define OPENTHREAD_CONFIG_IP6_FRAGMENTATION_ENABLE 1
#endif

#ifndef OPENTHREAD_CONFIG_IP6_MAX_DATAGRAM_LENGTH
#define OPENTHREAD_CONFIG_IP6_MAX_DATAGRAM_LENGTH 1280
#endif

/* ========================================================================
 * Fast Path Configuration
 * ======================================================================== */

/* Fast path queue sizes */
#ifndef COAP_RESPONSE_QUEUE_SIZE
#define COAP_RESPONSE_QUEUE_SIZE 256  /* Further reduced to save memory */
#endif

#ifndef COAP_PROCESSING_QUEUE_SIZE
#define COAP_PROCESSING_QUEUE_SIZE 128  /* Further reduced to save memory */
#endif

/* Fast path enable/disable */
#ifndef COAP_FAST_PATH_ENABLED
#define COAP_FAST_PATH_ENABLED 1
#endif

/* Force fast path regardless of memory constraints (for debugging) */
#ifndef COAP_FORCE_FAST_PATH
#define COAP_FORCE_FAST_PATH 1  /* Force enable for debugging */
#endif

/* ========================================================================
 * Memory Allocation Preferences
 * ======================================================================== */

/* Prefer PSRAM for large allocations */
#define COAP_PREFER_PSRAM 1

/* Minimum size for PSRAM allocation (smaller allocations use internal RAM) */
#define COAP_PSRAM_THRESHOLD 32  /* Reduced to use PSRAM more aggressively */

/* ========================================================================
 * Performance Tuning
 * ======================================================================== */

/* Reduce CoAP processing delays */
#ifndef COAP_RESOURCE_CHECK_TIME
#define COAP_RESOURCE_CHECK_TIME 1  /* Check every 1 second instead of default */
#endif

/* Increase default message size for better throughput */
#ifndef COAP_DEFAULT_MAX_PDU_RX_SIZE
#define COAP_DEFAULT_MAX_PDU_RX_SIZE 1024  /* Increased from default 1152 */
#endif

/* ========================================================================
 * Task Priorities and Stack Sizes
 * ======================================================================== */

/* CoAP processing task configuration */
#define COAP_PROCESSING_TASK_PRIORITY 6
#define COAP_PROCESSING_TASK_STACK_SIZE 8192  /* Increased back to prevent stack overflow */

/* Main CoAP server task configuration */
#define COAP_SERVER_TASK_PRIORITY 5
#define COAP_SERVER_TASK_STACK_SIZE 4096

#endif /* COAP_OPTIMIZATION_CONFIG_H_ */
