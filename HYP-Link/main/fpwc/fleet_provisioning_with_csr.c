/* Standard includes. */
#include <stdlib.h>
#include <stdio.h>
#include <stdint.h>
#include <stdbool.h>
#include <string.h>

/* POSIX includes. */
#include <unistd.h>
#include <errno.h>

/* Demo config. */
#include "configs/fpwc_config.h"

/* corePKCS11 includes. */
#include "core_pkcs11.h"
#include "core_pkcs11_config.h"

/* AWS IoT Fleet Provisioning Library. */
#include "fleet_provisioning.h"

/* Demo includes. */
#include "mqtt_operations.h"
#include "pkcs11_operations.h"
#include "fleet_provisioning_serializer.h"

#include "fleet_provisioning_with_csr.h"

#include "nvs.h"
#include "nvs_flash.h"

#include "esp_mac.h"

// Assuming these are already defined elsewhere in your code
#define NVS_NAMESPACE "tn" // thingname

/**
 * These configurations are required. Throw compilation error if it is not
 * defined.
 */
#ifndef PROVISIONING_TEMPLATE_NAME
#error "Please define PROVISIONING_TEMPLATE_NAME to the template name registered with AWS IoT Core in demo_config.h."
#endif
#ifndef CLAIM_CERT_PATH
#error "Please define path to claim certificate (CLAIM_CERT_PATH) in demo_config.h."
#endif
#ifndef CLAIM_PRIVATE_KEY_PATH
#error "Please define path to claim private key (CLAIM_PRIVATE_KEY_PATH) in demo_config.h."
#endif

/**
 * @brief The length of #PROVISIONING_TEMPLATE_NAME.
 */
#define PROVISIONING_TEMPLATE_NAME_LENGTH ((uint16_t)(sizeof(PROVISIONING_TEMPLATE_NAME) - 1))

/**
 * @brief Buffer to hold the provisioned AWS IoT Thing name.
 */
char thingName[MAX_THING_NAME_LENGTH];

/**
 * @brief Length of the AWS IoT Thing name.
 */
size_t thingNameLength;

/**
 * @brief The maximum number of times to run the loop in this demo.
 *
 * @note The demo loop is attempted to re-run only if it fails in an iteration.
 * Once the demo loop succeeds in an iteration, the demo exits successfully.
 */
#ifndef FLEET_PROV_MAX_DEMO_LOOP_COUNT
#define FLEET_PROV_MAX_DEMO_LOOP_COUNT (3)
#endif

/**
 * @brief Time in seconds to wait between retries of the demo loop if
 * demo loop fails.
 */
#define DELAY_BETWEEN_DEMO_RETRY_ITERATIONS_SECONDS (5)

/**
 * @brief Size of buffer in which to hold the certificate signing request (CSR).
 */
#define CSR_BUFFER_LENGTH 2048

/**
 * @brief Size of buffer in which to hold the certificate.
 */
#define CERT_BUFFER_LENGTH 2048

/**
 * @brief Size of buffer in which to hold the certificate id.
 *
 * See https://docs.aws.amazon.com/iot/latest/apireference/API_Certificate.html#iot-Type-Certificate-certificateId
 */
#define CERT_ID_BUFFER_LENGTH 64

/**
 * @brief Size of buffer in which to hold the certificate ownership token.
 */
#define OWNERSHIP_TOKEN_BUFFER_LENGTH 512

/**
 * @brief Status values of the Fleet Provisioning response.
 */
typedef enum
{
    ResponseNotReceived,
    ResponseAccepted,
    ResponseRejected
} ResponseStatus_t;

/*-----------------------------------------------------------*/

extern bool flag;

/**
 * @brief Status reported from the MQTT publish callback.
 */
static ResponseStatus_t responseStatus;

/**
 * @brief Buffer to hold responses received from the AWS IoT Fleet Provisioning
 * APIs. When the MQTT publish callback receives an expected Fleet Provisioning
 * accepted payload, it copies it into this buffer.
 * Allocated in PSRAM to save internal RAM.
 */
static uint8_t *payloadBuffer = NULL;

/**
 * @brief Length of the payload stored in #payloadBuffer. This is set by the
 * MQTT publish callback when it copies a received payload into #payloadBuffer.
 */
static size_t payloadLength;

/*-----------------------------------------------------------*/

/**
 * @brief Callback to receive the incoming publish messages from the MQTT
 * broker. Sets responseStatus if an expected CreateCertificateFromCsr or
 * RegisterThing response is received, and copies the response into
 * responseBuffer if the response is an accepted one.
 *
 * @param[in] pPublishInfo Pointer to publish info of the incoming publish.
 * @param[in] packetIdentifier Packet identifier of the incoming publish.
 */
static void provisioningPublishCallback(MQTTPublishInfo_t *pPublishInfo,
                                        uint16_t packetIdentifier);

/**
 * @brief Run the MQTT process loop to get a response.
 */
static bool waitForResponse(void);

/**
 * @brief Subscribe to the CreateCertificateFromCsr accepted and rejected topics.
 */
static bool subscribeToCsrResponseTopics(void);

/**
 * @brief Unsubscribe from the CreateCertificateFromCsr accepted and rejected topics.
 */
static bool unsubscribeFromCsrResponseTopics(void);

/**
 * @brief Subscribe to the RegisterThing accepted and rejected topics.
 */
static bool subscribeToRegisterThingResponseTopics(void);

/**
 * @brief Unsubscribe from the RegisterThing accepted and rejected topics.
 */
static bool unsubscribeFromRegisterThingResponseTopics(void);

extern void writeThingName(void);

/*-----------------------------------------------------------*/

static void provisioningPublishCallback(MQTTPublishInfo_t *pPublishInfo,
                                        uint16_t packetIdentifier)
{
    FleetProvisioningStatus_t status;
    FleetProvisioningTopic_t api;
    const char *cborDump;

    /* Silence compiler warnings about unused variables. */
    (void)packetIdentifier;

    status = FleetProvisioning_MatchTopic(pPublishInfo->pTopicName,
                                          pPublishInfo->topicNameLength, &api);

    if (status != FleetProvisioningSuccess)
    {
        LogWarn(("Unexpected publish message received. Topic: %.*s.",
                 (int)pPublishInfo->topicNameLength,
                 (const char *)pPublishInfo->pTopicName));
    }
    else
    {
        if (api == FleetProvCborCreateCertFromCsrAccepted)
        {
            LogInfo(("Received accepted response from Fleet Provisioning CreateCertificateFromCsr API."));

            cborDump = getStringFromCbor((const uint8_t *)pPublishInfo->pPayload, pPublishInfo->payloadLength);
            LogDebug(("Payload: %s", cborDump));
            free((void *)cborDump);

            responseStatus = ResponseAccepted;

            /* Copy the payload from the MQTT library's buffer to #payloadBuffer. */
            (void)memcpy((void *)payloadBuffer,
                         (const void *)pPublishInfo->pPayload,
                         (size_t)pPublishInfo->payloadLength);

            payloadLength = pPublishInfo->payloadLength;
        }
        else if (api == FleetProvCborCreateCertFromCsrRejected)
        {
            LogError(("Received rejected response from Fleet Provisioning CreateCertificateFromCsr API."));

            cborDump = getStringFromCbor((const uint8_t *)pPublishInfo->pPayload, pPublishInfo->payloadLength);
            LogError(("Payload: %s", cborDump));
            free((void *)cborDump);

            responseStatus = ResponseRejected;
        }
        else if (api == FleetProvCborRegisterThingAccepted)
        {
            LogInfo(("Received accepted response from Fleet Provisioning RegisterThing API."));

            cborDump = getStringFromCbor((const uint8_t *)pPublishInfo->pPayload, pPublishInfo->payloadLength);
            LogDebug(("Payload: %s", cborDump));
            free((void *)cborDump);

            responseStatus = ResponseAccepted;

            /* Copy the payload from the MQTT library's buffer to #payloadBuffer. */
            (void)memcpy((void *)payloadBuffer,
                         (const void *)pPublishInfo->pPayload,
                         (size_t)pPublishInfo->payloadLength);

            payloadLength = pPublishInfo->payloadLength;
        }
        else if (api == FleetProvCborRegisterThingRejected)
        {
            LogError(("Received rejected response from Fleet Provisioning RegisterThing API."));

            cborDump = getStringFromCbor((const uint8_t *)pPublishInfo->pPayload, pPublishInfo->payloadLength);
            LogError(("Payload: %s", cborDump));
            free((void *)cborDump);

            responseStatus = ResponseRejected;
        }
        else
        {
            LogError(("Received message on unexpected Fleet Provisioning topic. Topic: %.*s.",
                      (int)pPublishInfo->topicNameLength,
                      (const char *)pPublishInfo->pTopicName));
        }
    }
}
/*-----------------------------------------------------------*/

static bool waitForResponse(void)
{
    bool status = false;

    responseStatus = ResponseNotReceived;

    /* responseStatus is updated from the MQTT publish callback. */
    (void)ProcessLoopWithTimeout();

    if (responseStatus == ResponseNotReceived)
    {
        LogError(("Timed out waiting for response."));
    }

    if (responseStatus == ResponseAccepted)
    {
        status = true;
    }

    return status;
}
/*-----------------------------------------------------------*/

static bool subscribeToCsrResponseTopics(void)
{
    bool status;

    status = SubscribeToTopic(FP_CBOR_CREATE_CERT_ACCEPTED_TOPIC,
                              FP_CBOR_CREATE_CERT_ACCEPTED_LENGTH);

    if (status == false)
    {
        LogError(("Failed to subscribe to fleet provisioning topic: %.*s.",
                  FP_CBOR_CREATE_CERT_ACCEPTED_LENGTH,
                  FP_CBOR_CREATE_CERT_ACCEPTED_TOPIC));
    }

    if (status == true)
    {
        status = SubscribeToTopic(FP_CBOR_CREATE_CERT_REJECTED_TOPIC,
                                  FP_CBOR_CREATE_CERT_REJECTED_LENGTH);

        if (status == false)
        {
            LogError(("Failed to subscribe to fleet provisioning topic: %.*s.",
                      FP_CBOR_CREATE_CERT_REJECTED_LENGTH,
                      FP_CBOR_CREATE_CERT_REJECTED_TOPIC));
        }
    }

    return status;
}
/*-----------------------------------------------------------*/

static bool unsubscribeFromCsrResponseTopics(void)
{
    bool status;

    status = UnsubscribeFromTopic(FP_CBOR_CREATE_CERT_ACCEPTED_TOPIC,
                                  FP_CBOR_CREATE_CERT_ACCEPTED_LENGTH);

    if (status == false)
    {
        LogError(("Failed to unsubscribe from fleet provisioning topic: %.*s.",
                  FP_CBOR_CREATE_CERT_ACCEPTED_LENGTH,
                  FP_CBOR_CREATE_CERT_ACCEPTED_TOPIC));
    }

    if (status == true)
    {
        status = UnsubscribeFromTopic(FP_CBOR_CREATE_CERT_REJECTED_TOPIC,
                                      FP_CBOR_CREATE_CERT_REJECTED_LENGTH);

        if (status == false)
        {
            LogError(("Failed to unsubscribe from fleet provisioning topic: %.*s.",
                      FP_CBOR_CREATE_CERT_REJECTED_LENGTH,
                      FP_CBOR_CREATE_CERT_REJECTED_TOPIC));
        }
    }

    return status;
}
/*-----------------------------------------------------------*/

static bool subscribeToRegisterThingResponseTopics(void)
{
    bool status;

    status = SubscribeToTopic(FP_CBOR_REGISTER_ACCEPTED_TOPIC(PROVISIONING_TEMPLATE_NAME),
                              FP_CBOR_REGISTER_ACCEPTED_LENGTH(PROVISIONING_TEMPLATE_NAME_LENGTH));

    if (status == false)
    {
        LogError(("Failed to subscribe to fleet provisioning topic: %.*s.",
                  FP_CBOR_REGISTER_ACCEPTED_LENGTH(PROVISIONING_TEMPLATE_NAME_LENGTH),
                  FP_CBOR_REGISTER_ACCEPTED_TOPIC(PROVISIONING_TEMPLATE_NAME)));
    }

    if (status == true)
    {
        status = SubscribeToTopic(FP_CBOR_REGISTER_REJECTED_TOPIC(PROVISIONING_TEMPLATE_NAME),
                                  FP_CBOR_REGISTER_REJECTED_LENGTH(PROVISIONING_TEMPLATE_NAME_LENGTH));

        if (status == false)
        {
            LogError(("Failed to subscribe to fleet provisioning topic: %.*s.",
                      FP_CBOR_REGISTER_REJECTED_LENGTH(PROVISIONING_TEMPLATE_NAME_LENGTH),
                      FP_CBOR_REGISTER_REJECTED_TOPIC(PROVISIONING_TEMPLATE_NAME)));
        }
    }

    return status;
}
/*-----------------------------------------------------------*/

static bool unsubscribeFromRegisterThingResponseTopics(void)
{
    bool status;

    status = UnsubscribeFromTopic(FP_CBOR_REGISTER_ACCEPTED_TOPIC(PROVISIONING_TEMPLATE_NAME),
                                  FP_CBOR_REGISTER_ACCEPTED_LENGTH(PROVISIONING_TEMPLATE_NAME_LENGTH));

    if (status == false)
    {
        LogError(("Failed to unsubscribe from fleet provisioning topic: %.*s.",
                  FP_CBOR_REGISTER_ACCEPTED_LENGTH(PROVISIONING_TEMPLATE_NAME_LENGTH),
                  FP_CBOR_REGISTER_ACCEPTED_TOPIC(PROVISIONING_TEMPLATE_NAME)));
    }

    if (status == true)
    {
        status = UnsubscribeFromTopic(FP_CBOR_REGISTER_REJECTED_TOPIC(PROVISIONING_TEMPLATE_NAME),
                                      FP_CBOR_REGISTER_REJECTED_LENGTH(PROVISIONING_TEMPLATE_NAME_LENGTH));

        if (status == false)
        {
            LogError(("Failed to unsubscribe from fleet provisioning topic: %.*s.",
                      FP_CBOR_REGISTER_REJECTED_LENGTH(PROVISIONING_TEMPLATE_NAME_LENGTH),
                      FP_CBOR_REGISTER_REJECTED_TOPIC(PROVISIONING_TEMPLATE_NAME)));
        }
    }

    return status;
}

bool read_thingname()
{
    bool status = true;
    // Open NVS for reading
    nvs_handle_t handle;
    esp_err_t err = nvs_open(NVS_NAMESPACE, NVS_READONLY, &handle);
    // Read the length of thing_name saved in NVS
    err = nvs_get_str(handle, "name", NULL, &thingNameLength);

    // If the size is successfully obtained, read the data
    if (err == ESP_OK && thingNameLength > 0)
    {
        // Use the defined thingName array to read data
        err = nvs_get_str(handle, "name", thingName, &thingNameLength);
        if (err == ESP_OK)
        {
            // Successfully read thingName
            LogInfo(("Thing name from NVS: %s", thingName));
            LogInfo(("Thing name length: %d", (int)thingNameLength));
            writeThingName();
        }
        else
        {
            LogError(("Failed to read thing_name from NVS"));
            status = false;
        }
    }
    else
    {
        LogError(("Failed to read nvs, perform pre-set certificate authentication"));
        status = false;
    }
    // Closing the NVS handle
    nvs_close(handle);
    return status;
}

/*-----------------------------------------------------------*/

/* This example uses a single application task, which shows that how to use
 * the Fleet Provisioning library to generate and validate AWS IoT Fleet
 * Provisioning MQTT topics, and use the coreMQTT library to communicate with
 * the AWS IoT Fleet Provisioning APIs. */
void aws_iot_demo_main(void *arg)
{
    bool status = false;

    // Allocate payload buffer in PSRAM
    if (!payloadBuffer) {
        payloadBuffer = heap_caps_malloc(NETWORK_BUFFER_SIZE, MALLOC_CAP_SPIRAM | MALLOC_CAP_8BIT);
        if (!payloadBuffer) {
            // Fallback to internal RAM if PSRAM allocation fails
            payloadBuffer = heap_caps_malloc(NETWORK_BUFFER_SIZE, MALLOC_CAP_8BIT);
        }
        if (!payloadBuffer) {
            ESP_LOGE("FLEET_PROV", "Failed to allocate payload buffer");
            return;
        }
        ESP_LOGI("FLEET_PROV", "Payload buffer allocated in %s",
                 heap_caps_get_allocated_size(payloadBuffer) ? "PSRAM" : "Internal RAM");
    }

    /* Buffer for holding the CSR. */
    char csr[CSR_BUFFER_LENGTH] = {0};
    size_t csrLength = 0;
    /* Buffer for holding received certificate until it is saved. */
    char certificate[CERT_BUFFER_LENGTH];
    size_t certificateLength;
    /* Buffer for holding the certificate ID. */
    char certificateId[CERT_ID_BUFFER_LENGTH];
    size_t certificateIdLength;
    /* Buffer for holding the certificate ownership token. */
    char ownershipToken[OWNERSHIP_TOKEN_BUFFER_LENGTH];
    size_t ownershipTokenLength;
    bool connectionEstablished = false;
    CK_SESSION_HANDLE p11Session;
    int demoRunCount = 0;
    CK_RV pkcs11ret = CKR_OK;

    do
    {
        /* Initialize the buffer lengths to their max lengths. */
        certificateLength = CERT_BUFFER_LENGTH;
        certificateIdLength = CERT_ID_BUFFER_LENGTH;
        ownershipTokenLength = OWNERSHIP_TOKEN_BUFFER_LENGTH;

        /* Initialize the PKCS #11 module */
        pkcs11ret = xInitializePkcs11Session(&p11Session);

        LogInfo(("Connecting with AWS IoT...\n"));
        status = EstablishMqttSession(provisioningPublishCallback,
                                      p11Session,
                                      pkcs11configLABEL_DEVICE_CERTIFICATE_FOR_TLS,
                                      pkcs11configLABEL_DEVICE_PRIVATE_KEY_FOR_TLS);

        status = read_thingname();

        if (status == true)
        {
            LogInfo(("Connection successful!\n"));
            connectionEstablished = true;
        }
        else
        {
            LogError(("Connection failed. This should be the first connection. Pre-set certificate and private key...\n"));

            if (pkcs11ret != CKR_OK)
            {
                LogError(("Failed to initialize PKCS #11."));
                status = false;
            }
            else
            {
                /* Insert the claim credentials into the PKCS #11 module */
                status = loadClaimCredentials(p11Session,
                                              CLAIM_CERT_PATH,
                                              pkcs11configLABEL_CLAIM_CERTIFICATE,
                                              CLAIM_PRIVATE_KEY_PATH,
                                              pkcs11configLABEL_CLAIM_PRIVATE_KEY);

                if (status == false)
                {
                    LogError(("Failed to provision PKCS #11 with claim credentials."));
                }
            }

            /**** Connect to AWS IoT Core with provisioning claim credentials *****/

            /* We first use the claim credentials to connect to the broker. These
             * credentials should allow use of the RegisterThing API and one of the
             * CreateCertificatefromCsr or CreateKeysAndCertificate.
             * In this demo we use CreateCertificatefromCsr. */

            if (status == true)
            {
                /* Attempts to connect to the AWS IoT MQTT broker. If the
                 * connection fails, retries after a timeout. Timeout value will
                 * exponentially increase until maximum attempts are reached. */
                LogInfo(("Establishing MQTT session with claim certificate..."));
                status = EstablishMqttSession(provisioningPublishCallback,
                                              p11Session,
                                              pkcs11configLABEL_CLAIM_CERTIFICATE,
                                              pkcs11configLABEL_CLAIM_PRIVATE_KEY);

                if (status == false)
                {
                    LogError(("Failed to establish MQTT session."));
                }
                else
                {
                    LogInfo(("Established connection with claim credentials."));
                    connectionEstablished = true;
                }
            }

            /**** Call the CreateCertificateFromCsr API ***************************/

            /* We use the CreateCertificatefromCsr API to obtain a client certificate
             * for a key on the device by means of sending a certificate signing
             * request (CSR). */
            if (status == true)
            {
                /* Subscribe to the CreateCertificateFromCsr accepted and rejected
                 * topics. In this demo we use CBOR encoding for the payloads,
                 * so we use the CBOR variants of the topics. */
                status = subscribeToCsrResponseTopics();
            }

            if (status == true)
            {
                /* Create a new key and CSR. */
                status = generateKeyAndCsr(p11Session,
                                           pkcs11configLABEL_DEVICE_PRIVATE_KEY_FOR_TLS,
                                           pkcs11configLABEL_DEVICE_PUBLIC_KEY_FOR_TLS,
                                           csr,
                                           CSR_BUFFER_LENGTH,
                                           &csrLength);
            }

            if (status == true)
            {
                /* Create the request payload containing the CSR to publish to the
                 * CreateCertificateFromCsr APIs. */
                status = generateCsrRequest(payloadBuffer,
                                            NETWORK_BUFFER_SIZE,
                                            csr,
                                            csrLength,
                                            &payloadLength);
            }

            if (status == true)
            {
                /* Publish the CSR to the CreateCertificatefromCsr API. */
                PublishToTopic(FP_CBOR_CREATE_CERT_PUBLISH_TOPIC,
                               FP_CBOR_CREATE_CERT_PUBLISH_LENGTH,
                               (char *)payloadBuffer,
                               payloadLength);

                if (status == false)
                {
                    LogError(("Failed to publish to fleet provisioning topic: %.*s.",
                              FP_CBOR_CREATE_CERT_PUBLISH_LENGTH,
                              FP_CBOR_CREATE_CERT_PUBLISH_TOPIC));
                }
            }

            if (status == true)
            {
                /* Get the response to the CreateCertificatefromCsr request. */
                status = waitForResponse();
            }

            if (status == true)
            {
                /* From the response, extract the certificate, certificate ID, and
                 * certificate ownership token. */
                status = parseCsrResponse(payloadBuffer,
                                          payloadLength,
                                          certificate,
                                          &certificateLength,
                                          certificateId,
                                          &certificateIdLength,
                                          ownershipToken,
                                          &ownershipTokenLength);

                if (status == true)
                {
                    LogInfo(("Received certificate with Id: %.*s", (int)certificateIdLength, certificateId));
                }
            }

            if (status == true)
            {
                /* Save the certificate into PKCS #11. */
                status = loadCertificate(p11Session,
                                         certificate,
                                         pkcs11configLABEL_DEVICE_CERTIFICATE_FOR_TLS,
                                         certificateLength);
            }

            if (status == true)
            {
                /* Unsubscribe from the CreateCertificateFromCsr topics. */
                status = unsubscribeFromCsrResponseTopics();
            }

            /**** Call the RegisterThing API **************************************/

            /* We then use the RegisterThing API to activate the received certificate,
             * provision AWS IoT resources according to the provisioning template, and
             * receive device configuration. */
            if (status == true)
            {
                uint8_t mac[6];
                char macStr[18];
                esp_efuse_mac_get_default(mac);
                snprintf(macStr, 18, "%02X:%02X:%02X:%02X:%02X:%02X", mac[0], mac[1], mac[2], mac[3], mac[4], mac[5]);

                /* Create the request payload to publish to the RegisterThing API. */
                status = generateRegisterThingRequest(payloadBuffer,
                                                      NETWORK_BUFFER_SIZE,
                                                      ownershipToken,
                                                      ownershipTokenLength,
                                                      macStr,
                                                      (uint16_t) (sizeof(macStr) - 1),
                                                      &payloadLength);
            }

            if (status == true)
            {
                /* Subscribe to the RegisterThing response topics. */
                status = subscribeToRegisterThingResponseTopics();
            }

            if (status == true)
            {
                /* Publish the RegisterThing request. */
                PublishToTopic(FP_CBOR_REGISTER_PUBLISH_TOPIC(PROVISIONING_TEMPLATE_NAME),
                               FP_CBOR_REGISTER_PUBLISH_LENGTH(PROVISIONING_TEMPLATE_NAME_LENGTH),
                               (char *)payloadBuffer,
                               payloadLength);

                if (status == false)
                {
                    LogError(("Failed to publish to fleet provisioning topic: %.*s.",
                              FP_CBOR_REGISTER_PUBLISH_LENGTH(PROVISIONING_TEMPLATE_NAME_LENGTH),
                              FP_CBOR_REGISTER_PUBLISH_TOPIC(PROVISIONING_TEMPLATE_NAME)));
                }
            }

            if (status == true)
            {
                /* Get the response to the RegisterThing request. */
                status = waitForResponse();
            }

            if (status == true)
            {
                /* Extract the Thing name from the response. */
                thingNameLength = MAX_THING_NAME_LENGTH;
                status = parseRegisterThingResponse(payloadBuffer,
                                                    payloadLength,
                                                    thingName,
                                                    &thingNameLength);

                // Save in nvs
                if (status == true)
                {
                    LogInfo(("Received AWS IoT Thing name: %.*s", (int)thingNameLength, thingName));
                    // Write the Thing name and its length to NVS
                    nvs_handle_t handle;
                    esp_err_t err = nvs_open(NVS_NAMESPACE, NVS_READWRITE, &handle);
                    if (err == ESP_OK)
                    {
                        // Save the thingName to NVS
                        err = nvs_set_str(handle, "name", thingName);
                        if (err != ESP_OK)
                        {
                            LogError(("Failed to write thingName to NVS"));
                        }
                        else
                        {

                            // Commit the changes to NVS
                            err = nvs_commit(handle);
                            if (err != ESP_OK)
                            {
                                LogError(("Failed to commit NVS changes"));
                            }
                            writeThingName();
                        }

                        // Close the NVS handle
                        nvs_close(handle);
                    }
                    else
                    {
                        LogError(("Failed to open NVS for writing"));
                    }
                }

                if (status == true)
                {
                    LogInfo(("Received AWS IoT Thing name: %.*s", (int)thingNameLength, thingName));
                }
            }

            if (status == true)
            {
                /* Unsubscribe from the RegisterThing topics. */
                unsubscribeFromRegisterThingResponseTopics();
            }

            /**** Disconnect from AWS IoT Core ************************************/

            /* As we have completed the provisioning workflow, we disconnect from
             * the connection using the provisioning claim credentials. We will
             * establish a new MQTT connection with the newly provisioned
             * credentials. */
            if (connectionEstablished == true)
            {
                DisconnectMqttSession();
                connectionEstablished = false;
            }

            /**** Connect to AWS IoT Core with provisioned certificate ************/

            if (status == true)
            {
                LogInfo(("Establishing MQTT session with provisioned certificate..."));
                status = EstablishMqttSession(provisioningPublishCallback,
                                              p11Session,
                                              pkcs11configLABEL_DEVICE_CERTIFICATE_FOR_TLS,
                                              pkcs11configLABEL_DEVICE_PRIVATE_KEY_FOR_TLS);

                if (status != true)
                {
                    LogError(("Failed to establish MQTT session with provisioned "
                              "credentials. Verify on your AWS account that the "
                              "new certificate is active and has an attached IoT "
                              "Policy that allows the \"iot:Connect\" action."));
                }
                else
                {
                    LogInfo(("Sucessfully established connection with provisioned credentials."));
                    connectionEstablished = true;
                }
            }
        }

        /**** Finish **********************************************************/

        if (connectionEstablished == true)
        {
            /* Close the connection. */
            DisconnectMqttSession();
            connectionEstablished = false;
        }

        pkcs11CloseSession(p11Session);

        /**** Retry in case of failure ****************************************/

        /* Increment the demo run count. */
        demoRunCount++;

        if (status == true)
        {
            LogInfo(("Demo iteration %d is successful.", demoRunCount));
        }
        /* Attempt to retry a failed iteration of demo for up to #FLEET_PROV_MAX_DEMO_LOOP_COUNT times. */
        else if (demoRunCount < FLEET_PROV_MAX_DEMO_LOOP_COUNT)
        {
            LogWarn(("Demo iteration %d failed. Retrying...", demoRunCount));
            sleep(DELAY_BETWEEN_DEMO_RETRY_ITERATIONS_SECONDS);
        }
        /* Failed all #FLEET_PROV_MAX_DEMO_LOOP_COUNT demo iterations. */
        else
        {
            LogError(("All %d demo iterations failed.", FLEET_PROV_MAX_DEMO_LOOP_COUNT));
            esp_restart();
            break;
        }
    } while (status != true);

    /* Log demo success. */
    if (status == true)
    {
        LogInfo(("fpwc completed successfully."));
    }
    flag = true;
    vTaskDelete(NULL);
}
/*-----------------------------------------------------------*/
