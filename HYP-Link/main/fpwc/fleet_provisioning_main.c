#include <stdio.h>
#include <stdint.h>
#include <stddef.h>
#include <string.h>
#include "esp_system.h"
#include "nvs_flash.h"
#include "esp_event.h"
#include "esp_netif.h"
#include "protocol_examples_common.h"
#include "esp_spiffs.h"
#include "esp_log.h"

void aws_iot_demo_main( void * arg );

bool flag = false;

static const char *TAG = "FLEET_PROVISIONING_EXAMPLE";

esp_vfs_spiffs_conf_t conf = {
  .base_path = "/spiffs",
  .partition_label = "spiffs_storage",
  .max_files = 5,
  .format_if_mount_failed = false
};

void filesystem_init(void)
{
    ESP_LOGI(TAG, "Initializing SPIFFS");

    // Use settings defined above to initialize and mount SPIFFS filesystem.
    // Note: esp_vfs_spiffs_register is an all-in-one convenience function.
    esp_err_t ret = esp_vfs_spiffs_register(&conf);

    if (ret != ESP_OK) {
        if (ret == ESP_FAIL) {
            ESP_LOGE(TAG, "Failed to mount or format filesystem");
        } else if (ret == ESP_ERR_NOT_FOUND) {
            ESP_LOGE(TAG, "Failed to find SPIFFS partition");
        } else {
            ESP_LOGE(TAG, "Failed to initialize SPIFFS (%s)", esp_err_to_name(ret));
        }
        return;
    }

#if ESP_IDF_VERSION >= ESP_IDF_VERSION_VAL(4, 4, 0)
    ESP_LOGI(TAG, "Performing SPIFFS_check().");
    ret = esp_spiffs_check(conf.partition_label);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "SPIFFS_check() failed (%s)", esp_err_to_name(ret));
        return;
    } else {
        ESP_LOGI(TAG, "SPIFFS_check() successful");
    }
#endif

    size_t total = 0, used = 0;
    ret = esp_spiffs_info(conf.partition_label, &total, &used);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to get SPIFFS partition information (%s). Formatting...", esp_err_to_name(ret));
        esp_spiffs_format(conf.partition_label);
        return;
    } else {
        ESP_LOGI(TAG, "Partition size: total: %d, used: %d", total, used);
    }
}

void filesystem_deinit(void)
{
  // All done, unmount partition and disable SPIFFS
    esp_vfs_spiffs_unregister(conf.partition_label);
    ESP_LOGI(TAG, "SPIFFS unmounted");
}

/*
 * Prototypes for the demos that can be started from this project.  Note the
 * MQTT demo is not actually started until the network is already.
 */

void fleet_provisioning_init(void)
{
    ESP_LOGI(TAG, "[APP] Free memory: %"PRIu32" bytes", esp_get_free_heap_size());

    esp_log_level_set("*", ESP_LOG_INFO);

    filesystem_init();

    // Try to create task with PSRAM stack first
    BaseType_t result = xTaskCreateWithCaps(aws_iot_demo_main, "awsiotfleet", 12288, NULL, 6, NULL, MALLOC_CAP_SPIRAM);

    // Fallback to internal RAM if PSRAM stack creation fails
    if (result != pdPASS) {
        ESP_LOGW(TAG, "Failed to create fleet provisioning task with PSRAM stack, trying internal RAM");
        xTaskCreate(aws_iot_demo_main, "awsiotfleet", 12288, NULL, 6, NULL);
    } else {
        ESP_LOGI(TAG, "Fleet provisioning task created with PSRAM stack");
    }

    while(!flag){
        vTaskDelay(pdMS_TO_TICKS(100));
    }

    //filesystem_deinit(); // Uncomment to transfer sensor firmware
}
