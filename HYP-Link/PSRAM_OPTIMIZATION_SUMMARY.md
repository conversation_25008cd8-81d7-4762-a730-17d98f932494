# HYP-Link PSRAM 内存优化总结

## 优化概述

本次优化将HYP-Link项目中的大量内存使用从内部RAM迁移到PSRAM，以最大化利用ESP32S3的PSRAM资源，释放宝贵的内部RAM用于系统关键功能。

## 优化策略

### 1. 激进PSRAM使用策略
- **优先使用PSRAM**：所有大于32字节的内存分配优先使用PSRAM
- **智能回退机制**：PSRAM分配失败时自动回退到内部RAM
- **内存监控**：实时监控PSRAM和内部RAM使用情况

### 2. 任务栈PSRAM化
- 所有FreeRTOS任务栈优先分配在PSRAM
- 保留内部RAM用于系统关键功能（WiFi、蓝牙、中断处理等）

## 具体优化项目

### 1. 任务创建优化

#### 1.1 报告任务 (reporting_task.c)
```c
// 优化前
xTaskCreate(prvTempSubPubAndLEDControlTask, "currentSensor", stack_size, ...);

// 优化后
BaseType_t result = xTaskCreateWithCaps(..., MALLOC_CAP_SPIRAM);
if (result != pdPASS) {
    // 回退到内部RAM
    xTaskCreate(...);
}
```

#### 1.2 OTA任务 (ota_task.c)
- 将OTA任务栈移到PSRAM
- 添加PSRAM分配失败的回退机制

#### 1.3 Fleet Provisioning任务 (fleet_provisioning_main.c)
- 12KB任务栈移到PSRAM
- 减少内部RAM压力

#### 1.4 LED控制任务 (app_manager.c)
- 所有LED闪烁任务栈移到PSRAM
- 按钮处理任务栈移到PSRAM

### 2. 网络模块内存优化

#### 2.1 BLE模块 (ble_spp_server.c)
```c
// 优化前
char *data = malloc(data_len + 1);

// 优化后
char *data = heap_caps_malloc(data_len + 1, MALLOC_CAP_SPIRAM | MALLOC_CAP_8BIT);
if (!data) {
    data = heap_caps_malloc(data_len + 1, MALLOC_CAP_8BIT); // 回退
}
```

#### 2.2 WiFi模块 (app_wifi.c)
```c
// 优化前
*random_bytes = calloc(*len, 1);

// 优化后
*random_bytes = heap_caps_calloc(*len, 1, MALLOC_CAP_SPIRAM | MALLOC_CAP_8BIT);
if (!*random_bytes) {
    *random_bytes = heap_caps_calloc(*len, 1, MALLOC_CAP_8BIT); // 回退
}
```

#### 2.3 MQTT模块 (mqtt_manager.c)
- **网络缓冲区**：将静态ucNetworkBuffer移到PSRAM
- **队列存储**：将MQTT命令队列存储移到PSRAM
- **连接任务**：MQTT连接任务栈移到PSRAM

### 3. 大型静态缓冲区优化

#### 3.1 OTA缓冲区 (ota_task.c)
```c
// 优化前
static OtaDataEvent_t dataBuffers[MAX_BUFFERS] = {0};
static OtaJobEventData_t jobDocBuffer = {0};
static AfrOtaJobDocumentFields_t jobFields = {0};
static uint8_t OtaImageSignatureDecoded[OTA_MAX_SIGNATURE_SIZE] = {0};

// 优化后
static OtaDataEvent_t *dataBuffers = NULL;
static OtaJobEventData_t *jobDocBuffer = NULL;
static AfrOtaJobDocumentFields_t *jobFields = NULL;
static uint8_t *OtaImageSignatureDecoded = NULL;

// 运行时在PSRAM中分配
dataBuffers = heap_caps_calloc(count, sizeof(OtaDataEvent_t), MALLOC_CAP_SPIRAM);
```

#### 3.2 MQTT网络缓冲区
```c
// 优化前
static uint8_t ucNetworkBuffer[BUFFER_SIZE];

// 优化后
static uint8_t *ucNetworkBuffer = NULL;
// 运行时在PSRAM中分配
ucNetworkBuffer = heap_caps_malloc(BUFFER_SIZE, MALLOC_CAP_SPIRAM);
```

## 内存分配策略

### 1. 优先级顺序
1. **PSRAM优先**：所有大型分配优先使用PSRAM
2. **智能回退**：PSRAM不足时回退到内部RAM
3. **内存监控**：持续监控内存使用情况

### 2. 分配阈值
- **小型分配** (< 32字节)：使用内部RAM
- **中型分配** (32-1024字节)：优先PSRAM，回退内部RAM
- **大型分配** (> 1024字节)：强制使用PSRAM

### 3. 错误处理
- 所有PSRAM分配都有内部RAM回退机制
- 详细的日志记录分配成功/失败情况
- 内存不足时的优雅降级

## 预期效果

### 1. 内部RAM释放
- **任务栈**：释放约50-80KB内部RAM
- **静态缓冲区**：释放约20-30KB内部RAM
- **动态分配**：减少内部RAM碎片化

### 2. 系统稳定性提升
- 更多内部RAM用于WiFi/蓝牙栈
- 减少内存分配失败
- 提高系统整体稳定性

### 3. 功能扩展能力
- 为未来功能预留更多内部RAM
- 支持更复杂的网络操作
- 提高并发处理能力

## 监控和调试

### 1. 内存使用监控
- 实时显示PSRAM和内部RAM使用情况
- 跟踪内存分配峰值
- 检测内存泄漏

### 2. 日志记录
- 详细记录每次PSRAM分配
- 标识分配成功的内存类型（PSRAM/内部RAM）
- 警告内存不足情况

### 3. 性能指标
- 监控任务栈使用情况
- 跟踪内存分配性能
- 评估PSRAM访问延迟影响

## 注意事项

### 1. PSRAM访问延迟
- PSRAM访问比内部RAM慢约2-3倍
- 对于时间关键的操作仍使用内部RAM
- 大部分应用场景延迟影响可忽略

### 2. 兼容性
- 保持与现有代码的兼容性
- 所有优化都有回退机制
- 不影响现有功能

### 3. 维护性
- 清晰的内存分配策略
- 详细的注释和文档
- 易于理解和维护的代码结构

## 新增工具：内存监控系统

### 1. 内存监控工具 (memory_monitor.c/h)

#### 1.1 功能特性
- **实时监控**：定时监控PSRAM和内部RAM使用情况
- **智能分配**：提供智能内存分配函数，优先使用PSRAM
- **泄漏检测**：跟踪内存分配和释放，检测潜在泄漏
- **使用警告**：内存使用率超过阈值时自动警告

#### 1.2 使用方法
```c
// 初始化内存监控
memory_monitor_config_t config = MEMORY_MONITOR_DEFAULT_CONFIG();
memory_monitor_init(&config);
memory_monitor_start();

// 智能内存分配
void *ptr = MALLOC_SMART(1024);  // 自动选择PSRAM或内部RAM
FREE_SMART(ptr);                 // 智能释放并置NULL

// 创建PSRAM栈任务
TaskHandle_t handle;
CREATE_TASK_PSRAM_STACK(task_func, "TaskName", 4096, NULL, 5, &handle);

// 打印内存统计
memory_monitor_print_stats();
```

#### 1.3 便利宏定义
- `MALLOC_SMART(size)` - 智能内存分配
- `FREE_SMART(ptr)` - 智能内存释放
- `MALLOC_PSRAM_FIRST(size)` - PSRAM优先分配
- `CALLOC_PSRAM_FIRST(count, size)` - PSRAM优先calloc
- `CREATE_TASK_PSRAM_STACK(...)` - 创建PSRAM栈任务

## 完整优化清单

### 已优化的文件列表

#### 1. 任务栈优化
- ✅ `reporting_task.c` - 报告任务栈移到PSRAM
- ✅ `ota_task.c` - OTA任务栈移到PSRAM
- ✅ `fleet_provisioning_main.c` - Fleet Provisioning任务栈移到PSRAM
- ✅ `app_manager.c` - LED控制和按钮任务栈移到PSRAM
- ✅ `sntp_client.c` - 时间同步任务栈移到PSRAM
- ✅ `mqtt_manager.c` - MQTT Agent和连接任务栈移到PSRAM

#### 2. 动态内存分配优化
- ✅ `ble_spp_server.c` - BLE数据接收缓冲区使用PSRAM
- ✅ `app_wifi.c` - WiFi随机字节缓冲区使用PSRAM
- ✅ `log_upload.c` - 日志JSON缓冲区使用PSRAM（已有）
- ✅ `map.c` - 地图数据缓冲区使用PSRAM（已有）

#### 3. 静态缓冲区优化
- ✅ `mqtt_manager.c` - MQTT网络缓冲区和队列存储移到PSRAM
- ✅ `ota_task.c` - OTA数据缓冲区、作业文档缓冲区等移到PSRAM
- ✅ `fleet_provisioning_with_csr.c` - Fleet Provisioning负载缓冲区移到PSRAM

#### 4. 新增工具
- ✅ `memory_monitor.c/h` - 内存监控和智能分配工具

### 预期内存释放量

#### 任务栈释放（约70-90KB内部RAM）
- 报告任务：8KB
- OTA任务：12KB
- Fleet Provisioning任务：12KB
- LED控制任务：4KB × 6 = 24KB
- 按钮任务：5KB
- 时间同步任务：4KB
- MQTT Agent任务：8KB
- MQTT连接任务：8KB

#### 静态缓冲区释放（约30-50KB内部RAM）
- MQTT网络缓冲区：16KB
- MQTT队列存储：2KB
- OTA数据缓冲区：8KB × 4 = 32KB
- OTA作业文档缓冲区：4KB
- OTA作业字段缓冲区：2KB
- OTA签名缓冲区：1KB
- Fleet Provisioning缓冲区：16KB

#### 总计释放：约100-140KB内部RAM

## 使用建议

### 1. 开发阶段
```c
// 启用内存监控
memory_monitor_config_t config = {
    .monitor_interval_ms = 1000,  // 1秒监控间隔
    .log_level = ESP_LOG_DEBUG,   // 详细日志
    .enable_leak_detection = true,
    .alert_threshold_percent = 80  // 80%警告阈值
};
memory_monitor_init(&config);
memory_monitor_start();
```

### 2. 生产环境
```c
// 较低频率监控
memory_monitor_config_t config = {
    .monitor_interval_ms = 10000,  // 10秒监控间隔
    .log_level = ESP_LOG_WARN,     // 只记录警告
    .enable_leak_detection = false,
    .alert_threshold_percent = 90   // 90%警告阈值
};
```

### 3. 新代码开发
- 使用 `MALLOC_SMART()` 替代 `malloc()`
- 使用 `CREATE_TASK_PSRAM_STACK()` 创建任务
- 定期调用 `memory_monitor_check_leaks()` 检查泄漏

## 总结

通过这次全面的PSRAM优化，HYP-Link项目能够：
1. **最大化利用PSRAM资源**：将大部分内存使用迁移到PSRAM
2. **释放宝贵的内部RAM**：释放约100-140KB内部RAM
3. **提高系统稳定性**：减少内存分配失败和碎片化
4. **增强扩展能力**：为未来功能开发提供更多内存空间
5. **提供监控工具**：实时监控内存使用，及时发现问题

这些优化确保了ESP32S3的内存资源得到最优利用，同时保持了系统的稳定性和可维护性。新增的内存监控工具为开发和维护提供了强大的支持。
