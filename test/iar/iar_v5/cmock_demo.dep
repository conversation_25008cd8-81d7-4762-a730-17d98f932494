<?xml version="1.0" encoding="iso-8859-1"?>

<project>
  <fileVersion>2</fileVersion>
  <fileChecksum>3270150602</fileChecksum>
  <configuration>
    <name>Binary</name>
    <outputs>
      <file>$PROJ_DIR$\Binary\Obj\TimerConductor.o</file>
      <file>$TOOLKIT_DIR$\inc\DLib_Defaults.h</file>
      <file>$TOOLKIT_DIR$\inc\DLib_Threads.h</file>
      <file>$PROJ_DIR$\Binary\Obj\TimerConductor.pbi</file>
      <file>$PROJ_DIR$\Binary\List\TimerInterruptConfigurator.lst</file>
      <file>$PROJ_DIR$\Binary\Obj\AdcTemperatureSensor.o</file>
      <file>$PROJ_DIR$\..\..\test\system\src\TimerInterruptConfigurator.h</file>
      <file>$PROJ_DIR$\..\..\test\system\src\TimerModel.h</file>
      <file>$TOOLKIT_DIR$\inc\ymath.h</file>
      <file>$PROJ_DIR$\Binary\Obj\Cstartup_SAM7.o</file>
      <file>$TOOLKIT_DIR$\inc\DLib_Product.h</file>
      <file>$TOOLKIT_DIR$\inc\math.h</file>
      <file>$PROJ_DIR$\Binary\Exe\cmock_demo.hex</file>
      <file>$PROJ_DIR$\Binary\Obj\UsartPutChar.pbi</file>
      <file>$PROJ_DIR$\..\..\test\system\src\TimerInterruptHandler.h</file>
      <file>$PROJ_DIR$\Binary\Obj\AdcConductor.pbi</file>
      <file>$PROJ_DIR$\Binary\List\TimerConfigurator.lst</file>
      <file>$PROJ_DIR$\Binary\List\TaskScheduler.lst</file>
      <file>$PROJ_DIR$\Binary\List\TemperatureCalculator.lst</file>
      <file>$PROJ_DIR$\Binary\List\UsartConductor.lst</file>
      <file>$PROJ_DIR$\Binary\Obj\UsartConductor.o</file>
      <file>$PROJ_DIR$\Binary\Obj\TimerModel.o</file>
      <file>$PROJ_DIR$\Binary\Obj\UsartConfigurator.pbi</file>
      <file>$PROJ_DIR$\Binary\Obj\TimerInterruptConfigurator.o</file>
      <file>$PROJ_DIR$\Binary\List\Cstartup.lst</file>
      <file>$PROJ_DIR$\..\..\test\system\src\Executor.h</file>
      <file>$PROJ_DIR$\incIAR\project.h</file>
      <file>$PROJ_DIR$\Binary\Obj\Executor.pbi</file>
      <file>$PROJ_DIR$\Binary\List\TimerConductor.lst</file>
      <file>$PROJ_DIR$\Binary\Obj\TimerModel.pbi</file>
      <file>$TOOLKIT_DIR$\inc\intrinsics.h</file>
      <file>$PROJ_DIR$\Binary\Obj\Model.pbi</file>
      <file>$PROJ_DIR$\Binary\Obj\UsartBaudRateRegisterCalculator.o</file>
      <file>$PROJ_DIR$\..\..\examples\src\UsartBaudRateRegisterCalculator.h</file>
      <file>$PROJ_DIR$\..\..\examples\src\UsartModel.h</file>
      <file>$PROJ_DIR$\Binary\Obj\TaskScheduler.pbi</file>
      <file>$PROJ_DIR$\Binary\Obj\Executor.o</file>
      <file>$PROJ_DIR$\Binary\Obj\TemperatureCalculator.o</file>
      <file>$PROJ_DIR$\Binary\Obj\Cstartup_SAM7.pbi</file>
      <file>$PROJ_DIR$\..\..\test\system\src\AT91SAM7X256.h</file>
      <file>$PROJ_DIR$\Binary\Obj\IntrinsicsWrapper.pbi</file>
      <file>$PROJ_DIR$\..\..\examples\src\UsartHardware.h</file>
      <file>$PROJ_DIR$\Binary\List\Main.lst</file>
      <file>$PROJ_DIR$\..\..\examples\src\UsartConfigurator.h</file>
      <file>$PROJ_DIR$\..\..\examples\src\UsartTransmitBufferStatus.h</file>
      <file>$PROJ_DIR$\Binary\List\TimerHardware.lst</file>
      <file>$PROJ_DIR$\..\..\test\system\src\UsartBaudRateRegisterCalculator.h</file>
      <file>$PROJ_DIR$\..\..\test\system\src\TemperatureCalculator.h</file>
      <file>$PROJ_DIR$\..\..\test\system\src\UsartPutChar.h</file>
      <file>$PROJ_DIR$\..\..\test\system\src\TaskScheduler.h</file>
      <file>$PROJ_DIR$\Binary\List\UsartTransmitBufferStatus.lst</file>
      <file>$PROJ_DIR$\Binary\Obj\AdcTemperatureSensor.pbi</file>
      <file>$PROJ_DIR$\Binary\Obj\Main.pbi</file>
      <file>$PROJ_DIR$\Binary\List\UsartModel.lst</file>
      <file>$PROJ_DIR$\Binary\Obj\TemperatureFilter.pbi</file>
      <file>$PROJ_DIR$\Binary\List\AdcHardware.lst</file>
      <file>$PROJ_DIR$\Binary\List\AdcTemperatureSensor.lst</file>
      <file>$PROJ_DIR$\Binary\Obj\UsartTransmitBufferStatus.pbi</file>
      <file>$PROJ_DIR$\Binary\Obj\AdcHardware.pbi</file>
      <file>$PROJ_DIR$\Binary\Obj\TemperatureCalculator.pbi</file>
      <file>$TOOLKIT_DIR$\lib\dl4t_tl_in.a</file>
      <file>$TOOLKIT_DIR$\inc\ycheck.h</file>
      <file>$PROJ_DIR$\..\..\test\system\src\ModelConfig.h</file>
      <file>$PROJ_DIR$\Binary\List\AdcConductor.lst</file>
      <file>$PROJ_DIR$\Binary\List\AdcHardwareConfigurator.lst</file>
      <file>$PROJ_DIR$\Binary\Obj\TimerHardware.o</file>
      <file>$PROJ_DIR$\Binary\Obj\TimerInterruptHandler.o</file>
      <file>$TOOLKIT_DIR$\inc\stdio.h</file>
      <file>$PROJ_DIR$\Binary\Obj\UsartHardware.o</file>
      <file>$PROJ_DIR$\Binary\Obj\TimerInterruptHandler.pbi</file>
      <file>$PROJ_DIR$\Binary\Obj\UsartModel.o</file>
      <file>$TOOLKIT_DIR$\inc\DLib_Config_Normal.h</file>
      <file>$PROJ_DIR$\Binary\List\TemperatureFilter.lst</file>
      <file>$PROJ_DIR$\Binary\List\UsartPutChar.lst</file>
      <file>$PROJ_DIR$\Binary\Obj\UsartConfigurator.o</file>
      <file>$PROJ_DIR$\..\..\test\system\src\UsartConductor.h</file>
      <file>$PROJ_DIR$\Binary\List\AdcModel.lst</file>
      <file>$PROJ_DIR$\..\..\test\system\src\TemperatureFilter.h</file>
      <file>$PROJ_DIR$\..\..\test\system\src\Types.h</file>
      <file>$PROJ_DIR$\..\..\test\system\src\TimerConfigurator.h</file>
      <file>$TOOLKIT_DIR$\inc\yvals.h</file>
      <file>$PROJ_DIR$\..\..\test\system\src\UsartModel.h</file>
      <file>$PROJ_DIR$\Binary\Obj\UsartTransmitBufferStatus.o</file>
      <file>$TOOLKIT_DIR$\lib\rt4t_al.a</file>
      <file>$PROJ_DIR$\Binary\List\UsartHardware.lst</file>
      <file>$TOOLKIT_DIR$\inc\ysizet.h</file>
      <file>$PROJ_DIR$\Binary\Obj\AdcModel.o</file>
      <file>$PROJ_DIR$\Binary\Obj\AdcConductor.o</file>
      <file>$PROJ_DIR$\Binary\Exe\cmock_demo.out</file>
      <file>$PROJ_DIR$\..\..\test\system\src\AdcConductor.h</file>
      <file>$PROJ_DIR$\..\..\test\system\src\AdcTemperatureSensor.h</file>
      <file>$PROJ_DIR$\Binary\Obj\UsartModel.pbi</file>
      <file>$PROJ_DIR$\..\..\test\system\src\TimerHardware.h</file>
      <file>$PROJ_DIR$\..\..\test\system\src\AdcModel.h</file>
      <file>$PROJ_DIR$\..\..\test\system\src\AdcHardware.h</file>
      <file>$PROJ_DIR$\Binary\List\UsartBaudRateRegisterCalculator.lst</file>
      <file>$PROJ_DIR$\..\..\test\system\src\Model.h</file>
      <file>$TOOLKIT_DIR$\lib\shs_l.a</file>
      <file>$PROJ_DIR$\..\..\test\system\src\UsartConfigurator.h</file>
      <file>$PROJ_DIR$\Binary\Obj\TimerInterruptConfigurator.pbi</file>
      <file>$PROJ_DIR$\Binary\List\UsartConfigurator.lst</file>
      <file>$PROJ_DIR$\Binary\List\TimerModel.lst</file>
      <file>$PROJ_DIR$\..\..\examples\src\Executor.h</file>
      <file>$PROJ_DIR$\Binary\Obj\IntrinsicsWrapper.o</file>
      <file>$PROJ_DIR$\..\..\examples\src\TimerConfigurator.h</file>
      <file>$PROJ_DIR$\..\..\examples\src\UsartConductor.h</file>
      <file>$PROJ_DIR$\..\..\test\system\src\AdcTemperatureSensor.c</file>
      <file>$PROJ_DIR$\..\..\examples\src\TimerConductor.h</file>
      <file>$PROJ_DIR$\Binary\Obj\AdcHardwareConfigurator.o</file>
      <file>$TOOLKIT_DIR$\inc\xencoding_limits.h</file>
      <file>$PROJ_DIR$\..\..\test\system\src\Main.c</file>
      <file>$PROJ_DIR$\..\..\test\system\src\AdcHardwareConfigurator.c</file>
      <file>$PROJ_DIR$\..\..\test\system\src\AdcConductor.c</file>
      <file>$PROJ_DIR$\..\..\test\system\src\AdcHardware.c</file>
      <file>$PROJ_DIR$\..\..\examples\src\TimerModel.h</file>
      <file>$PROJ_DIR$\..\..\test\system\src\AdcModel.c</file>
      <file>$PROJ_DIR$\..\..\examples\src\TimerInterruptHandler.h</file>
      <file>$PROJ_DIR$\..\..\test\system\src\Executor.c</file>
      <file>$PROJ_DIR$\..\..\examples\src\Model.h</file>
      <file>$PROJ_DIR$\..\..\test\system\src\Model.c</file>
      <file>$PROJ_DIR$\..\..\examples\src\IntrinsicsWrapper.h</file>
      <file>$PROJ_DIR$\..\..\examples\src\UsartPutChar.h</file>
      <file>$PROJ_DIR$\..\..\examples\src\TimerInterruptConfigurator.h</file>
      <file>$PROJ_DIR$\..\..\test\system\src\TemperatureFilter.c</file>
      <file>$PROJ_DIR$\..\..\test\system\src\TimerConfigurator.c</file>
      <file>$PROJ_DIR$\..\..\test\system\src\TemperatureCalculator.c</file>
      <file>$PROJ_DIR$\..\..\test\system\src\TaskScheduler.c</file>
      <file>$PROJ_DIR$\..\..\test\system\src\TimerInterruptConfigurator.c</file>
      <file>$PROJ_DIR$\Binary\Obj\AdcModel.pbi</file>
      <file>$PROJ_DIR$\..\..\test\system\src\TimerConductor.c</file>
      <file>$PROJ_DIR$\..\..\test\system\src\TimerInterruptHandler.c</file>
      <file>$PROJ_DIR$\Binary\Obj\AdcHardware.o</file>
      <file>$PROJ_DIR$\..\..\test\system\src\UsartConfigurator.c</file>
      <file>$PROJ_DIR$\..\..\test\system\src\TimerHardware.c</file>
      <file>$PROJ_DIR$\..\..\test\system\src\UsartTransmitBufferStatus.c</file>
      <file>$PROJ_DIR$\..\..\examples\src\AdcModel.h</file>
      <file>$PROJ_DIR$\..\..\test\system\src\TimerModel.c</file>
      <file>$PROJ_DIR$\..\..\test\system\src\UsartBaudRateRegisterCalculator.c</file>
      <file>$PROJ_DIR$\..\..\test\system\src\UsartConductor.c</file>
      <file>$PROJ_DIR$\..\..\examples\src\AdcHardware.h</file>
      <file>$PROJ_DIR$\..\..\examples\src\TemperatureFilter.h</file>
      <file>$PROJ_DIR$\..\..\test\system\src\UsartHardware.c</file>
      <file>$PROJ_DIR$\..\..\test\system\src\UsartModel.c</file>
      <file>$PROJ_DIR$\..\..\test\system\src\UsartPutChar.c</file>
      <file>$PROJ_DIR$\..\..\examples\src\AdcTemperatureSensor.h</file>
      <file>$PROJ_DIR$\..\..\examples\src\ModelConfig.h</file>
      <file>$PROJ_DIR$\..\..\examples\src\AdcHardwareConfigurator.h</file>
      <file>$PROJ_DIR$\..\..\examples\src\Types.h</file>
      <file>$PROJ_DIR$\..\..\examples\src\TemperatureCalculator.h</file>
      <file>$PROJ_DIR$\..\..\examples\src\AdcConductor.h</file>
      <file>$PROJ_DIR$\..\..\examples\src\AT91SAM7X256.h</file>
      <file>$PROJ_DIR$\..\..\examples\src\TaskScheduler.h</file>
      <file>$PROJ_DIR$\..\..\examples\src\TimerHardware.h</file>
      <file>$PROJ_DIR$\Binary\Obj\TimerHardware.pbi</file>
      <file>$PROJ_DIR$\Binary\Obj\TimerConfigurator.o</file>
      <file>$PROJ_DIR$\Binary\List\Model.lst</file>
      <file>$PROJ_DIR$\Binary\Obj\Cstartup.o</file>
      <file>$PROJ_DIR$\Binary\Obj\TaskScheduler.o</file>
      <file>$PROJ_DIR$\Binary\Obj\TemperatureFilter.o</file>
      <file>$PROJ_DIR$\..\..\test\system\src\TimerConductor.h</file>
      <file>$PROJ_DIR$\Binary\Obj\Model.o</file>
      <file>$PROJ_DIR$\..\..\test\system\src\UsartTransmitBufferStatus.h</file>
      <file>$PROJ_DIR$\Binary\Obj\UsartHardware.pbi</file>
      <file>$PROJ_DIR$\Binary\List\TimerInterruptHandler.lst</file>
      <file>$PROJ_DIR$\Binary\Obj\cmock_demo.pbd</file>
      <file>$PROJ_DIR$\..\..\test\system\src\UsartHardware.h</file>
      <file>$PROJ_DIR$\..\..\test\system\src\AdcHardwareConfigurator.h</file>
      <file>$PROJ_DIR$\Binary\Obj\UsartConductor.pbi</file>
      <file>$PROJ_DIR$\srcIAR\Cstartup_SAM7.c</file>
      <file>$PROJ_DIR$\..\..\examples\src\AdcHardwareConfigurator.c</file>
      <file>$PROJ_DIR$\..\..\examples\src\AdcConductor.c</file>
      <file>$PROJ_DIR$\..\..\examples\src\AdcHardware.c</file>
      <file>$PROJ_DIR$\..\..\examples\src\AdcModel.c</file>
      <file>$PROJ_DIR$\..\..\examples\src\AdcTemperatureSensor.c</file>
      <file>$PROJ_DIR$\..\..\examples\src\Executor.c</file>
      <file>$PROJ_DIR$\..\..\examples\src\Main.c</file>
      <file>$PROJ_DIR$\..\..\examples\src\IntrinsicsWrapper.c</file>
      <file>$PROJ_DIR$\..\..\examples\src\Model.c</file>
      <file>$PROJ_DIR$\..\..\examples\src\TemperatureCalculator.c</file>
      <file>$PROJ_DIR$\Resource\at91SAM7X256_FLASH.icf</file>
      <file>$PROJ_DIR$\..\..\examples\src\TaskScheduler.c</file>
      <file>$PROJ_DIR$\..\..\examples\src\TimerInterruptConfigurator.c</file>
      <file>$PROJ_DIR$\..\..\examples\src\TemperatureFilter.c</file>
      <file>$PROJ_DIR$\..\..\examples\src\TimerConductor.c</file>
      <file>$PROJ_DIR$\..\..\examples\src\TimerConfigurator.c</file>
      <file>$PROJ_DIR$\..\..\examples\src\TimerHardware.c</file>
      <file>$PROJ_DIR$\..\..\examples\src\TimerInterruptHandler.c</file>
      <file>$PROJ_DIR$\..\..\examples\src\TimerModel.c</file>
      <file>$PROJ_DIR$\..\..\examples\src\UsartModel.c</file>
      <file>$PROJ_DIR$\..\..\examples\src\UsartBaudRateRegisterCalculator.c</file>
      <file>$PROJ_DIR$\..\..\examples\src\UsartConductor.c</file>
      <file>$PROJ_DIR$\..\..\examples\src\UsartConfigurator.c</file>
      <file>$PROJ_DIR$\..\..\examples\src\UsartHardware.c</file>
      <file>$PROJ_DIR$\..\..\examples\src\UsartTransmitBufferStatus.c</file>
      <file>$PROJ_DIR$\..\..\examples\src\UsartPutChar.c</file>
      <file>$PROJ_DIR$\srcIAR\Cstartup.s</file>
      <file>$PROJ_DIR$\Binary\Obj\UsartPutChar.o</file>
      <file>$PROJ_DIR$\Binary\Obj\TimerConfigurator.pbi</file>
      <file>$PROJ_DIR$\Binary\Obj\Main.o</file>
      <file>$PROJ_DIR$\Binary\List\Executor.lst</file>
      <file>$PROJ_DIR$\incIAR\AT91SAM7X-EK.h</file>
      <file>$PROJ_DIR$\incIAR\lib_AT91SAM7X256.h</file>
      <file>$PROJ_DIR$\incIAR\AT91SAM7X256_inc.h</file>
      <file>$PROJ_DIR$\Binary\Obj\AdcHardwareConfigurator.pbi</file>
      <file>$PROJ_DIR$\Binary\Obj\UsartBaudRateRegisterCalculator.pbi</file>
    </outputs>
    <file>
      <name>[ROOT_NODE]</name>
      <outputs>
        <tool>
          <name>ILINK</name>
          <file> 88</file>
        </tool>
      </outputs>
    </file>
    <file>
      <name>$PROJ_DIR$\Binary\Exe\cmock_demo.out</name>
      <outputs>
        <tool>
          <name>OBJCOPY</name>
          <file> 12</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>ILINK</name>
          <file> 179 87 131 108 86 5 156 9 36 103 198 160 157 37 158 0 154 65 23 66 21 32 20 74 68 70 196 82 97 83 60</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\test\system\src\AdcTemperatureSensor.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 51</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 56 5</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 78 39 90</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 78 39 90</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\test\system\src\Main.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 52</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 42 198</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 78 39 25 96 49 47 77 75 165 98 48 81 46 161 159 92 79 6 14 7 89 94 166 90 93</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 78 39 25 96 49 47 77 75 165 98 48 81 46 161 159 92 79 6 14 7 89 94 166 90 93</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\test\system\src\AdcHardwareConfigurator.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 203</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 64 108</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 78 39 166 62</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 78 39 166 62</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\test\system\src\AdcConductor.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 15</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 63 87</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 78 39 89 93 94</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 78 39 89 93 94</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\test\system\src\AdcHardware.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 58</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 55 131</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 78 39 94 166 90</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 78 39 94 166 90</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\test\system\src\AdcModel.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 128</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 76 86</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 78 39 93 49 47 77</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 78 39 93 49 47 77</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\test\system\src\Executor.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 27</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 199 36</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 78 39 25 96 75 159 89 30 61</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 78 39 25 96 75 159 89 30 61</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\test\system\src\Model.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 31</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 155 160</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 96 78 39 49 77</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 96 78 39 49 77</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\test\system\src\TemperatureFilter.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 54</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 72 158</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 78 39 77 11 61 8 80 1 10 109 2</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 78 39 77 11 61 8 80 1 71 10 109 2</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\test\system\src\TimerConfigurator.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 197</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 16 154</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 78 39 79 6</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 78 39 79 6</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\test\system\src\TemperatureCalculator.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 59</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 18 37</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 78 39 47 11 61 8 80 1 10 109 2</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 78 39 47 11 61 8 80 1 71 10 109 2</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\test\system\src\TaskScheduler.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 35</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 17 157</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 78 39 49</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 78 39 49</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\test\system\src\TimerInterruptConfigurator.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 99</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 4 23</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 78 39 6 14</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 78 39 6 14</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\test\system\src\TimerConductor.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 3</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 28 0</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 78 39 159 7 92 14</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 78 39 159 7 92 14</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\test\system\src\TimerInterruptHandler.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 69</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 163 66</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 78 39 14 6</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 78 39 14 6</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\test\system\src\UsartConfigurator.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 22</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 100 74</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 78 39 98</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 78 39 98</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\test\system\src\TimerHardware.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 153</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 45 65</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 78 39 92 79</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 78 39 92 79</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\test\system\src\UsartTransmitBufferStatus.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 57</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 50 82</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 78 39 161</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 78 39 161</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\test\system\src\TimerModel.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 29</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 101 21</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 78 39 7 49</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 78 39 7 49</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\test\system\src\UsartBaudRateRegisterCalculator.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 204</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 95 32</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 78 39 46</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 78 39 46</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\test\system\src\UsartConductor.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 167</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 19 20</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 78 39 75 165 81 49</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 78 39 75 165 81 49</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\test\system\src\UsartHardware.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 162</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 84 68</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 78 39 165 98 48</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 78 39 165 98 48</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\test\system\src\UsartModel.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 91</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 53 70</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 78 39 81 62 46 77 67 61 80 1 10 109 2 85 11 8</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 78 39 81 62 46 77 67 61 80 1 71 10 109 2 85 11 8</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\test\system\src\UsartPutChar.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 13</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 73 196</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 78 39 48 161</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 78 39 48 161</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\Binary\Obj\cmock_demo.pbd</name>
      <inputs>
        <tool>
          <name>BILINK</name>
          <file> 15 58 203 128 51 38 27 40 52 31 35 59 54 3 197 153 99 69 29 204 167 22 162 91 13 57</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\srcIAR\Cstartup_SAM7.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 38</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 26 30 61 200 150 201</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\examples\src\AdcHardwareConfigurator.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 203</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 147 150 146 145</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\examples\src\AdcConductor.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 15</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 63 87 131 108 86 5 36 103 198 160 157 37 158 0 154 65 23 66 21 32 20 74 68 70 196 82 9</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 147 150 149 135 139</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 147 150 149 135 139 146 144 145 151 148 140 102 118 105 107 120 30 61 41 43 121 34 33 44 152 104 122 116 114 11 8 80 1 71 10 109 2 67 85 26 200 201</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\examples\src\AdcHardware.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 58</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 147 150 139 146 144</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\examples\src\AdcModel.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 128</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 147 150 135 151 148 140</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\examples\src\AdcTemperatureSensor.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 51</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 147 150 144</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\examples\src\Executor.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 27</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 147 150 102 118 105 107 149 120</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\examples\src\Main.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 52</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 147 150 120 102 118 151 148 140 105 41 43 121 34 33 44 107 152 104 122 116 114 149 139 146 144 135</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\examples\src\IntrinsicsWrapper.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 40</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 120 30 61</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\examples\src\Model.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 31</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 118 147 150 151 140</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\examples\src\TemperatureCalculator.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 59</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 147 150 148 11 61 8 80 1 10 109 2</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\examples\src\TaskScheduler.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 35</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 147 150 151</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\examples\src\TimerInterruptConfigurator.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 99</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 147 150 122 116</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\examples\src\TemperatureFilter.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 54</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 147 150 140 11 61 8 80 1 10 109 2</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\examples\src\TimerConductor.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 3</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 147 150 107 114 152 116</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\examples\src\TimerConfigurator.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 197</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 147 150 104 122</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\examples\src\TimerHardware.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 153</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 147 150 152 104</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\examples\src\TimerInterruptHandler.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 69</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 147 150 116 122</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\examples\src\TimerModel.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 29</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 147 150 114 151</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\examples\src\UsartModel.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 91</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 147 150 34 145 33 140 67 61 80 1 10 109 2 85 11 8</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\examples\src\UsartBaudRateRegisterCalculator.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 204</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 147 150 33</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\examples\src\UsartConductor.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 167</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 147 150 105 41 34 151</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\examples\src\UsartConfigurator.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 22</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 147 150 43</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\examples\src\UsartHardware.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 162</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 147 150 41 43 121</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\examples\src\UsartTransmitBufferStatus.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 57</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 147 150 44</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\examples\src\UsartPutChar.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 13</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 147 150 121 44</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\srcIAR\Cstartup.s</name>
      <outputs>
        <tool>
          <name>AARM</name>
          <file> 156 24</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>AARM</name>
          <file> 202</file>
        </tool>
      </inputs>
    </file>
    <forcedrebuild>
      <name>$PROJ_DIR$\srcIAR\Cstartup_SAM7.c</name>
      <tool>ICCARM</tool>
    </forcedrebuild>
    <forcedrebuild>
      <name>$PROJ_DIR$\..\..\examples\src\AdcHardwareConfigurator.c</name>
      <tool>ICCARM</tool>
    </forcedrebuild>
    <forcedrebuild>
      <name>$PROJ_DIR$\..\..\examples\src\AdcHardware.c</name>
      <tool>ICCARM</tool>
    </forcedrebuild>
    <forcedrebuild>
      <name>$PROJ_DIR$\..\..\examples\src\AdcModel.c</name>
      <tool>ICCARM</tool>
    </forcedrebuild>
    <forcedrebuild>
      <name>$PROJ_DIR$\..\..\examples\src\AdcTemperatureSensor.c</name>
      <tool>ICCARM</tool>
    </forcedrebuild>
    <forcedrebuild>
      <name>$PROJ_DIR$\..\..\examples\src\Executor.c</name>
      <tool>ICCARM</tool>
    </forcedrebuild>
    <forcedrebuild>
      <name>$PROJ_DIR$\..\..\examples\src\Main.c</name>
      <tool>ICCARM</tool>
    </forcedrebuild>
    <forcedrebuild>
      <name>$PROJ_DIR$\..\..\examples\src\IntrinsicsWrapper.c</name>
      <tool>ICCARM</tool>
    </forcedrebuild>
    <forcedrebuild>
      <name>$PROJ_DIR$\..\..\examples\src\Model.c</name>
      <tool>ICCARM</tool>
    </forcedrebuild>
    <forcedrebuild>
      <name>$PROJ_DIR$\..\..\examples\src\TemperatureCalculator.c</name>
      <tool>ICCARM</tool>
    </forcedrebuild>
    <forcedrebuild>
      <name>$PROJ_DIR$\..\..\examples\src\TaskScheduler.c</name>
      <tool>ICCARM</tool>
    </forcedrebuild>
    <forcedrebuild>
      <name>$PROJ_DIR$\..\..\examples\src\TimerInterruptConfigurator.c</name>
      <tool>ICCARM</tool>
    </forcedrebuild>
    <forcedrebuild>
      <name>$PROJ_DIR$\..\..\examples\src\TemperatureFilter.c</name>
      <tool>ICCARM</tool>
    </forcedrebuild>
    <forcedrebuild>
      <name>$PROJ_DIR$\..\..\examples\src\TimerConductor.c</name>
      <tool>ICCARM</tool>
    </forcedrebuild>
    <forcedrebuild>
      <name>$PROJ_DIR$\..\..\examples\src\TimerConfigurator.c</name>
      <tool>ICCARM</tool>
    </forcedrebuild>
    <forcedrebuild>
      <name>$PROJ_DIR$\..\..\examples\src\TimerHardware.c</name>
      <tool>ICCARM</tool>
    </forcedrebuild>
    <forcedrebuild>
      <name>$PROJ_DIR$\..\..\examples\src\TimerInterruptHandler.c</name>
      <tool>ICCARM</tool>
    </forcedrebuild>
    <forcedrebuild>
      <name>$PROJ_DIR$\..\..\examples\src\TimerModel.c</name>
      <tool>ICCARM</tool>
    </forcedrebuild>
    <forcedrebuild>
      <name>$PROJ_DIR$\..\..\examples\src\UsartModel.c</name>
      <tool>ICCARM</tool>
    </forcedrebuild>
    <forcedrebuild>
      <name>$PROJ_DIR$\..\..\examples\src\UsartBaudRateRegisterCalculator.c</name>
      <tool>ICCARM</tool>
    </forcedrebuild>
    <forcedrebuild>
      <name>$PROJ_DIR$\..\..\examples\src\UsartConductor.c</name>
      <tool>ICCARM</tool>
    </forcedrebuild>
    <forcedrebuild>
      <name>$PROJ_DIR$\..\..\examples\src\UsartConfigurator.c</name>
      <tool>ICCARM</tool>
    </forcedrebuild>
    <forcedrebuild>
      <name>$PROJ_DIR$\..\..\examples\src\UsartHardware.c</name>
      <tool>ICCARM</tool>
    </forcedrebuild>
    <forcedrebuild>
      <name>$PROJ_DIR$\..\..\examples\src\UsartTransmitBufferStatus.c</name>
      <tool>ICCARM</tool>
    </forcedrebuild>
    <forcedrebuild>
      <name>$PROJ_DIR$\..\..\examples\src\UsartPutChar.c</name>
      <tool>ICCARM</tool>
    </forcedrebuild>
  </configuration>
  <configuration>
    <name>FLASH_Debug</name>
    <outputs>
      <file>$TOOLKIT_DIR$\inc\DLib_Defaults.h</file>
      <file>$TOOLKIT_DIR$\inc\DLib_Threads.h</file>
      <file>$PROJ_DIR$\..\..\test\system\src\TimerInterruptConfigurator.h</file>
      <file>$PROJ_DIR$\..\..\test\system\src\TimerModel.h</file>
      <file>$TOOLKIT_DIR$\inc\ymath.h</file>
      <file>$TOOLKIT_DIR$\inc\DLib_Product.h</file>
      <file>$TOOLKIT_DIR$\inc\math.h</file>
      <file>$PROJ_DIR$\..\..\test\system\src\TimerInterruptHandler.h</file>
      <file>$PROJ_DIR$\..\..\test\system\src\Executor.h</file>
      <file>$PROJ_DIR$\incIAR\project.h</file>
      <file>$TOOLKIT_DIR$\inc\intrinsics.h</file>
      <file>$PROJ_DIR$\..\..\examples\src\UsartBaudRateRegisterCalculator.h</file>
      <file>$PROJ_DIR$\..\..\examples\src\UsartModel.h</file>
      <file>$PROJ_DIR$\..\..\test\system\src\AT91SAM7X256.h</file>
      <file>$PROJ_DIR$\..\..\examples\src\UsartHardware.h</file>
      <file>$PROJ_DIR$\..\..\examples\src\UsartConfigurator.h</file>
      <file>$PROJ_DIR$\..\..\examples\src\UsartTransmitBufferStatus.h</file>
      <file>$PROJ_DIR$\..\..\test\system\src\UsartBaudRateRegisterCalculator.h</file>
      <file>$PROJ_DIR$\..\..\test\system\src\TemperatureCalculator.h</file>
      <file>$PROJ_DIR$\..\..\test\system\src\UsartPutChar.h</file>
      <file>$PROJ_DIR$\..\..\test\system\src\TaskScheduler.h</file>
      <file>$TOOLKIT_DIR$\lib\dl4t_tl_in.a</file>
      <file>$TOOLKIT_DIR$\inc\ycheck.h</file>
      <file>$PROJ_DIR$\..\..\test\system\src\ModelConfig.h</file>
      <file>$TOOLKIT_DIR$\inc\stdio.h</file>
      <file>$TOOLKIT_DIR$\inc\DLib_Config_Normal.h</file>
      <file>$PROJ_DIR$\..\..\test\system\src\UsartConductor.h</file>
      <file>$PROJ_DIR$\..\..\test\system\src\TemperatureFilter.h</file>
      <file>$PROJ_DIR$\..\..\test\system\src\Types.h</file>
      <file>$PROJ_DIR$\..\..\test\system\src\TimerConfigurator.h</file>
      <file>$TOOLKIT_DIR$\inc\yvals.h</file>
      <file>$PROJ_DIR$\..\..\test\system\src\UsartModel.h</file>
      <file>$TOOLKIT_DIR$\lib\rt4t_al.a</file>
      <file>$TOOLKIT_DIR$\inc\ysizet.h</file>
      <file>$PROJ_DIR$\..\..\test\system\src\AdcConductor.h</file>
      <file>$PROJ_DIR$\..\..\test\system\src\AdcTemperatureSensor.h</file>
      <file>$PROJ_DIR$\..\..\test\system\src\TimerHardware.h</file>
      <file>$PROJ_DIR$\..\..\test\system\src\AdcModel.h</file>
      <file>$PROJ_DIR$\..\..\test\system\src\AdcHardware.h</file>
      <file>$PROJ_DIR$\..\..\test\system\src\Model.h</file>
      <file>$TOOLKIT_DIR$\lib\shs_l.a</file>
      <file>$PROJ_DIR$\..\..\test\system\src\UsartConfigurator.h</file>
      <file>$PROJ_DIR$\..\..\examples\src\Executor.h</file>
      <file>$PROJ_DIR$\..\..\examples\src\TimerConfigurator.h</file>
      <file>$PROJ_DIR$\..\..\examples\src\UsartConductor.h</file>
      <file>$PROJ_DIR$\..\..\test\system\src\AdcTemperatureSensor.c</file>
      <file>$PROJ_DIR$\..\..\examples\src\TimerConductor.h</file>
      <file>$TOOLKIT_DIR$\inc\xencoding_limits.h</file>
      <file>$PROJ_DIR$\FLASH_Debug\Obj\cmock_demo.pbd</file>
      <file>$PROJ_DIR$\..\..\test\system\src\Main.c</file>
      <file>$PROJ_DIR$\..\..\test\system\src\AdcHardwareConfigurator.c</file>
      <file>$PROJ_DIR$\..\..\test\system\src\AdcConductor.c</file>
      <file>$PROJ_DIR$\..\..\test\system\src\AdcHardware.c</file>
      <file>$PROJ_DIR$\..\..\examples\src\TimerModel.h</file>
      <file>$PROJ_DIR$\..\..\test\system\src\AdcModel.c</file>
      <file>$PROJ_DIR$\..\..\examples\src\TimerInterruptHandler.h</file>
      <file>$PROJ_DIR$\..\..\test\system\src\Executor.c</file>
      <file>$PROJ_DIR$\..\..\examples\src\Model.h</file>
      <file>$PROJ_DIR$\..\..\test\system\src\Model.c</file>
      <file>$PROJ_DIR$\..\..\examples\src\IntrinsicsWrapper.h</file>
      <file>$PROJ_DIR$\..\..\examples\src\UsartPutChar.h</file>
      <file>$PROJ_DIR$\..\..\examples\src\TimerInterruptConfigurator.h</file>
      <file>$PROJ_DIR$\..\..\test\system\src\TemperatureFilter.c</file>
      <file>$PROJ_DIR$\..\..\test\system\src\TimerConfigurator.c</file>
      <file>$PROJ_DIR$\..\..\test\system\src\TemperatureCalculator.c</file>
      <file>$PROJ_DIR$\..\..\test\system\src\TaskScheduler.c</file>
      <file>$PROJ_DIR$\..\..\test\system\src\TimerInterruptConfigurator.c</file>
      <file>$PROJ_DIR$\..\..\test\system\src\TimerConductor.c</file>
      <file>$PROJ_DIR$\..\..\test\system\src\TimerInterruptHandler.c</file>
      <file>$PROJ_DIR$\..\..\test\system\src\UsartConfigurator.c</file>
      <file>$PROJ_DIR$\..\..\test\system\src\TimerHardware.c</file>
      <file>$PROJ_DIR$\..\..\test\system\src\UsartTransmitBufferStatus.c</file>
      <file>$PROJ_DIR$\..\..\examples\src\AdcModel.h</file>
      <file>$PROJ_DIR$\..\..\test\system\src\TimerModel.c</file>
      <file>$PROJ_DIR$\..\..\test\system\src\UsartBaudRateRegisterCalculator.c</file>
      <file>$PROJ_DIR$\..\..\test\system\src\UsartConductor.c</file>
      <file>$PROJ_DIR$\..\..\examples\src\AdcHardware.h</file>
      <file>$PROJ_DIR$\..\..\examples\src\TemperatureFilter.h</file>
      <file>$PROJ_DIR$\..\..\test\system\src\UsartHardware.c</file>
      <file>$PROJ_DIR$\..\..\test\system\src\UsartModel.c</file>
      <file>$PROJ_DIR$\..\..\test\system\src\UsartPutChar.c</file>
      <file>$PROJ_DIR$\..\..\examples\src\AdcTemperatureSensor.h</file>
      <file>$PROJ_DIR$\..\..\examples\src\ModelConfig.h</file>
      <file>$PROJ_DIR$\..\..\examples\src\AdcHardwareConfigurator.h</file>
      <file>$PROJ_DIR$\..\..\examples\src\Types.h</file>
      <file>$PROJ_DIR$\..\..\examples\src\TemperatureCalculator.h</file>
      <file>$PROJ_DIR$\..\..\examples\src\AdcConductor.h</file>
      <file>$PROJ_DIR$\..\..\examples\src\AT91SAM7X256.h</file>
      <file>$PROJ_DIR$\..\..\examples\src\TaskScheduler.h</file>
      <file>$PROJ_DIR$\..\..\examples\src\TimerHardware.h</file>
      <file>$PROJ_DIR$\..\..\test\system\src\TimerConductor.h</file>
      <file>$PROJ_DIR$\..\..\test\system\src\UsartTransmitBufferStatus.h</file>
      <file>$PROJ_DIR$\..\..\test\system\src\UsartHardware.h</file>
      <file>$PROJ_DIR$\FLASH_Debug\Obj\Main.o</file>
      <file>$PROJ_DIR$\..\..\test\system\src\AdcHardwareConfigurator.h</file>
      <file>$PROJ_DIR$\FLASH_Debug\List\TimerHardware.lst</file>
      <file>$PROJ_DIR$\FLASH_Debug\List\ext_irq.lst</file>
      <file>$PROJ_DIR$\FLASH_Debug\Obj\Cstartup.o</file>
      <file>$PROJ_DIR$\FLASH_Debug\Obj\TimerConfigurator.pbi</file>
      <file>$PROJ_DIR$\FLASH_Debug\List\UsartHardware.lst</file>
      <file>$PROJ_DIR$\..\src\ext_irq.c</file>
      <file>$PROJ_DIR$\FLASH_Debug\Obj\interrupt_Usart.o</file>
      <file>$PROJ_DIR$\FLASH_Debug\Obj\interrupt_Usart.pbi</file>
      <file>$PROJ_DIR$\FLASH_Debug\Obj\TimerModel.o</file>
      <file>$PROJ_DIR$\FLASH_Debug\Obj\main.pbi</file>
      <file>$PROJ_DIR$\FLASH_Debug\List\Model.lst</file>
      <file>$PROJ_DIR$\FLASH_Debug\Obj\AdcModel.o</file>
      <file>$PROJ_DIR$\FLASH_Debug\Obj\UsartHardware.o</file>
      <file>$PROJ_DIR$\FLASH_Debug\Obj\UsartPutChar.o</file>
      <file>$PROJ_DIR$\FLASH_Debug\Obj\TemperatureCalculator.pbi</file>
      <file>$PROJ_DIR$\FLASH_Debug\List\AdcConductor.lst</file>
      <file>$PROJ_DIR$\FLASH_Debug\Obj\UsartTransmitBufferStatus.pbi</file>
      <file>$PROJ_DIR$\FLASH_Debug\Obj\AdcConductor.pbi</file>
      <file>$PROJ_DIR$\FLASH_Debug\Obj\Model.pbi</file>
      <file>$PROJ_DIR$\FLASH_Debug\Obj\Cstartup_SAM7.o</file>
      <file>$PROJ_DIR$\FLASH_Debug\Obj\IntrinsicsWrapper.o</file>
      <file>$PROJ_DIR$\FLASH_Debug\Obj\IntrinsicsWrapper.pbi</file>
      <file>$PROJ_DIR$\FLASH_Debug\Obj\AdcHardware.o</file>
      <file>$PROJ_DIR$\FLASH_Debug\Obj\UsartPutChar.pbi</file>
      <file>$PROJ_DIR$\FLASH_Debug\Obj\UsartBaudRateRegisterCalculator.pbi</file>
      <file>$PROJ_DIR$\FLASH_Debug\List\interrupt_timer.lst</file>
      <file>$PROJ_DIR$\FLASH_Debug\List\Cstartup_SAM7.lst</file>
      <file>$PROJ_DIR$\FLASH_Debug\Obj\AdcHardwareConfigurator.o</file>
      <file>$PROJ_DIR$\FLASH_Debug\List\AdcHardwareConfigurator.lst</file>
      <file>$PROJ_DIR$\FLASH_Debug\Obj\TemperatureFilter.pbi</file>
      <file>$PROJ_DIR$\FLASH_Debug\Obj\TimerConductor.pbi</file>
      <file>$PROJ_DIR$\..\..\include\lib_AT91SAM7X256.h</file>
      <file>$PROJ_DIR$\FLASH_Debug\Obj\TaskScheduler.o</file>
      <file>$PROJ_DIR$\FLASH_Debug\Obj\TemperatureFilter.o</file>
      <file>$PROJ_DIR$\FLASH_Debug\Obj\ext_irq.pbi</file>
      <file>$PROJ_DIR$\..\..\include\AT91SAM7X256.h</file>
      <file>$PROJ_DIR$\FLASH_Debug\Obj\AdcModel.pbi</file>
      <file>$PROJ_DIR$\FLASH_Debug\Obj\TimerInterruptConfigurator.o</file>
      <file>$PROJ_DIR$\FLASH_Debug\List\AdcModel.lst</file>
      <file>$PROJ_DIR$\FLASH_Debug\List\interrupt_Usart.lst</file>
      <file>$PROJ_DIR$\FLASH_Debug\List\Cstartup.lst</file>
      <file>$PROJ_DIR$\FLASH_Debug\Obj\UsartHardware.pbi</file>
      <file>$PROJ_DIR$\FLASH_Debug\List\TimerModel.lst</file>
      <file>$PROJ_DIR$\FLASH_Debug\Obj\UsartConductor.o</file>
      <file>$PROJ_DIR$\FLASH_Debug\Obj\UsartConfigurator.o</file>
      <file>$PROJ_DIR$\FLASH_Debug\List\UsartPutChar.lst</file>
      <file>$PROJ_DIR$\FLASH_Debug\List\TemperatureFilter.lst</file>
      <file>$PROJ_DIR$\FLASH_Debug\Obj\TimerInterruptHandler.pbi</file>
      <file>$PROJ_DIR$\FLASH_Debug\List\TemperatureCalculator.lst</file>
      <file>$PROJ_DIR$\FLASH_Debug\List\UsartTransmitBufferStatus.lst</file>
      <file>$PROJ_DIR$\FLASH_Debug\Obj\UsartModel.pbi</file>
      <file>$PROJ_DIR$\FLASH_Debug\List\UsartConductor.lst</file>
      <file>$PROJ_DIR$\FLASH_Debug\List\TimerConfigurator.lst</file>
      <file>$PROJ_DIR$\FLASH_Debug\Obj\Model.o</file>
      <file>$PROJ_DIR$\FLASH_Debug\List\Executor.lst</file>
      <file>$PROJ_DIR$\FLASH_Debug\List\UsartModel.lst</file>
      <file>$PROJ_DIR$\FLASH_Debug\List\TimerInterruptHandler.lst</file>
      <file>$PROJ_DIR$\FLASH_Debug\List\Main.lst</file>
      <file>$PROJ_DIR$\FLASH_Debug\List\TimerInterruptConfigurator.lst</file>
      <file>$PROJ_DIR$\FLASH_Debug\Exe\cmock_demo.out</file>
      <file>$PROJ_DIR$\FLASH_Debug\Obj\TimerHardware.pbi</file>
      <file>$PROJ_DIR$\FLASH_Debug\Obj\TaskScheduler.pbi</file>
      <file>$PROJ_DIR$\FLASH_Debug\Obj\TimerModel.pbi</file>
      <file>$PROJ_DIR$\FLASH_Debug\Obj\AdcConductor.o</file>
      <file>$PROJ_DIR$\FLASH_Debug\Obj\AdcTemperatureSensor.o</file>
      <file>$PROJ_DIR$\..\src\AT91SAM7X-EK.h</file>
      <file>$PROJ_DIR$\FLASH_Debug\Obj\UsartTransmitBufferStatus.o</file>
      <file>$PROJ_DIR$\FLASH_Debug\Obj\AdcHardware.pbi</file>
      <file>$PROJ_DIR$\FLASH_Debug\Obj\UsartBaudRateRegisterCalculator.o</file>
      <file>$PROJ_DIR$\FLASH_Debug\Obj\ext_irq.o</file>
      <file>$PROJ_DIR$\FLASH_Debug\Obj\AdcTemperatureSensor.pbi</file>
      <file>$PROJ_DIR$\FLASH_Debug\Obj\TemperatureCalculator.o</file>
      <file>$PROJ_DIR$\FLASH_Debug\Obj\TimerHardware.o</file>
      <file>$PROJ_DIR$\FLASH_Debug\Obj\UsartModel.o</file>
      <file>$PROJ_DIR$\FLASH_Debug\List\UsartConfigurator.lst</file>
      <file>$PROJ_DIR$\FLASH_Debug\Obj\TimerConductor.o</file>
      <file>$PROJ_DIR$\srcIAR\project.h</file>
      <file>$PROJ_DIR$\FLASH_Debug\List\UsartBaudRateRegisterCalculator.lst</file>
      <file>$PROJ_DIR$\FLASH_Debug\Obj\AdcHardwareConfigurator.pbi</file>
      <file>$PROJ_DIR$\FLASH_Debug\Obj\TimerInterruptHandler.o</file>
      <file>$PROJ_DIR$\FLASH_Debug\List\TimerConductor.lst</file>
      <file>$PROJ_DIR$\FLASH_Debug\Obj\Cstartup_SAM7.pbi</file>
      <file>$PROJ_DIR$\FLASH_Debug\Obj\UsartConductor.pbi</file>
      <file>$PROJ_DIR$\FLASH_Debug\Obj\UsartConfigurator.pbi</file>
      <file>$PROJ_DIR$\FLASH_Debug\Obj\Executor.o</file>
      <file>$PROJ_DIR$\FLASH_Debug\List\AdcHardware.lst</file>
      <file>$PROJ_DIR$\FLASH_Debug\Obj\Executor.pbi</file>
      <file>$PROJ_DIR$\..\src\interrupt_timer.c</file>
      <file>$PROJ_DIR$\..\src\interrupt_Usart.c</file>
      <file>$PROJ_DIR$\FLASH_Debug\Obj\TimerInterruptConfigurator.pbi</file>
      <file>$PROJ_DIR$\..\src\main.c</file>
      <file>$PROJ_DIR$\FLASH_Debug\Obj\TimerConfigurator.o</file>
      <file>$PROJ_DIR$\FLASH_Debug\List\AdcTemperatureSensor.lst</file>
      <file>$PROJ_DIR$\FLASH_Debug\List\TaskScheduler.lst</file>
      <file>$PROJ_DIR$\FLASH_Debug\Obj\interrupt_timer.o</file>
      <file>$PROJ_DIR$\FLASH_Debug\List\IntrinsicsWrapper.lst</file>
      <file>$PROJ_DIR$\FLASH_Debug\Obj\interrupt_timer.pbi</file>
      <file>$PROJ_DIR$\srcIAR\Cstartup_SAM7.c</file>
      <file>$PROJ_DIR$\..\..\examples\src\AdcHardwareConfigurator.c</file>
      <file>$PROJ_DIR$\..\..\examples\src\AdcConductor.c</file>
      <file>$PROJ_DIR$\..\..\examples\src\AdcHardware.c</file>
      <file>$PROJ_DIR$\..\..\examples\src\AdcModel.c</file>
      <file>$PROJ_DIR$\..\..\examples\src\AdcTemperatureSensor.c</file>
      <file>$PROJ_DIR$\..\..\examples\src\Executor.c</file>
      <file>$PROJ_DIR$\..\..\examples\src\Main.c</file>
      <file>$PROJ_DIR$\..\..\examples\src\IntrinsicsWrapper.c</file>
      <file>$PROJ_DIR$\..\..\examples\src\Model.c</file>
      <file>$PROJ_DIR$\..\..\examples\src\TemperatureCalculator.c</file>
      <file>$PROJ_DIR$\Resource\at91SAM7X256_FLASH.icf</file>
      <file>$PROJ_DIR$\..\..\examples\src\TaskScheduler.c</file>
      <file>$PROJ_DIR$\..\..\examples\src\TimerInterruptConfigurator.c</file>
      <file>$PROJ_DIR$\..\..\examples\src\TemperatureFilter.c</file>
      <file>$PROJ_DIR$\..\..\examples\src\TimerConductor.c</file>
      <file>$PROJ_DIR$\..\..\examples\src\TimerConfigurator.c</file>
      <file>$PROJ_DIR$\..\..\examples\src\TimerHardware.c</file>
      <file>$PROJ_DIR$\..\..\examples\src\TimerInterruptHandler.c</file>
      <file>$PROJ_DIR$\..\..\examples\src\TimerModel.c</file>
      <file>$PROJ_DIR$\..\..\examples\src\UsartModel.c</file>
      <file>$PROJ_DIR$\..\..\examples\src\UsartBaudRateRegisterCalculator.c</file>
      <file>$PROJ_DIR$\..\..\examples\src\UsartConductor.c</file>
      <file>$PROJ_DIR$\..\..\examples\src\UsartConfigurator.c</file>
      <file>$PROJ_DIR$\..\..\examples\src\UsartHardware.c</file>
      <file>$PROJ_DIR$\..\..\examples\src\UsartTransmitBufferStatus.c</file>
      <file>$PROJ_DIR$\..\..\examples\src\UsartPutChar.c</file>
      <file>$PROJ_DIR$\srcIAR\Cstartup.s</file>
      <file>$PROJ_DIR$\incIAR\AT91SAM7X-EK.h</file>
      <file>$PROJ_DIR$\incIAR\lib_AT91SAM7X256.h</file>
      <file>$PROJ_DIR$\incIAR\AT91SAM7X256_inc.h</file>
    </outputs>
    <file>
      <name>[ROOT_NODE]</name>
      <outputs>
        <tool>
          <name>ILINK</name>
          <file> 154</file>
        </tool>
      </outputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\test\system\src\AdcTemperatureSensor.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 165</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 187 159</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>ICCARM</name>
          <file> 28 13 35</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\FLASH_Debug\Obj\cmock_demo.pbd</name>
      <inputs>
        <tool>
          <name>BILINK</name>
          <file> 112 162 173 131 165 176 181 116 113 156 109 124 125 98 155 184 142 157 119 177 178 136 145 118 111 104</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\test\system\src\Main.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 104</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 152 93</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>ICCARM</name>
          <file> 28 13 8 39 20 18 27 26 92 41 19 31 17 91 90 36 29 2 7 3 34 38 94 35 37</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\test\system\src\AdcHardwareConfigurator.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 173</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 123 122</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>ICCARM</name>
          <file> 28 13 94 23</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\test\system\src\AdcConductor.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 112</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 110 158</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>ICCARM</name>
          <file> 28 13 34 37 38</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\test\system\src\AdcHardware.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 162</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 180 117</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>ICCARM</name>
          <file> 28 13 38 94 35</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\test\system\src\AdcModel.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 131</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 133 106</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>ICCARM</name>
          <file> 28 13 37 20 18 27</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\test\system\src\Executor.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 181</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 149 179</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>ICCARM</name>
          <file> 28 13 8 39 26 90 34 10 22</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\test\system\src\Model.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 113</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 105 148</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>ICCARM</name>
          <file> 39 28 13 20 27</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\test\system\src\TemperatureFilter.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 124</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 141 128</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>ICCARM</name>
          <file> 28 13 27 6 22 4 30 0 25 5 47 1</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\test\system\src\TimerConfigurator.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 98</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 147 186</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>ICCARM</name>
          <file> 28 13 29 2</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\test\system\src\TemperatureCalculator.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 109</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 143 166</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>ICCARM</name>
          <file> 28 13 18 6 22 4 30 0 25 5 47 1</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\test\system\src\TaskScheduler.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 156</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 188 127</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>ICCARM</name>
          <file> 28 13 20</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\test\system\src\TimerInterruptConfigurator.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 184</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 153 132</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>ICCARM</name>
          <file> 28 13 2 7</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\test\system\src\TimerConductor.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 125</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 175 170</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>ICCARM</name>
          <file> 28 13 90 3 36 7</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\test\system\src\TimerInterruptHandler.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 142</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 151 174</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>ICCARM</name>
          <file> 28 13 7 2</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\test\system\src\UsartConfigurator.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 178</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 169 139</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>ICCARM</name>
          <file> 28 13 41</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\test\system\src\TimerHardware.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 155</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 95 167</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>ICCARM</name>
          <file> 28 13 36 29</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\test\system\src\UsartTransmitBufferStatus.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 111</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 144 161</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>ICCARM</name>
          <file> 28 13 91</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\test\system\src\TimerModel.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 157</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 137 103</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>ICCARM</name>
          <file> 28 13 3 20</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\test\system\src\UsartBaudRateRegisterCalculator.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 119</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 172 163</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>ICCARM</name>
          <file> 28 13 17</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\test\system\src\UsartConductor.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 177</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 146 138</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>ICCARM</name>
          <file> 28 13 26 92 31 20</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\test\system\src\UsartHardware.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 136</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 99 107</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>ICCARM</name>
          <file> 28 13 92 41 19</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\test\system\src\UsartModel.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 145</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 150 168</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>ICCARM</name>
          <file> 28 13 31 23 17 27 24 22 30 0 25 5 47 1 33 6 4</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\test\system\src\UsartPutChar.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 118</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 140 108</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>ICCARM</name>
          <file> 28 13 19 91</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\src\ext_irq.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 129</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 96 164</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 171 10 22 160 130 126</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 171 10 22 160 130 126</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\FLASH_Debug\Exe\cmock_demo.out</name>
      <inputs>
        <tool>
          <name>ILINK</name>
          <file> 203 158 117 122 106 159 97 114 179 115 93 148 127 166 128 170 186 167 132 174 103 163 138 139 107 168 108 161 40 32 21</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\src\interrupt_timer.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 191</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 120 189</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 171 10 22 160 130 126</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 171 10 22 160 130 126</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\src\interrupt_Usart.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 102</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 134 101</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 171 10 22 160 130 126</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 171 10 22 160 130 126</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\src\main.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 104</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 152 93</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 171 10 22 160 130 126</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 171 10 22 160 130 126</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\srcIAR\Cstartup_SAM7.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 176</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 121 114</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 9 10 22 220 221</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 9 10 22 220 87 221</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\examples\src\AdcHardwareConfigurator.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 173</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 123 122</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 84 87 83 82</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 84 87 83 82</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\examples\src\AdcConductor.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 112</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 110 158</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 84 87 86 72 76</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 84 87 86 72 76</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\examples\src\AdcHardware.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 162</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 180 117</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 84 87 76 83 81</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 84 87 76 83 81</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\examples\src\AdcModel.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 131</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 133 106</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 84 87 72 88 85 77</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 84 87 72 88 85 77</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\examples\src\AdcTemperatureSensor.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 165</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 187 159</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 84 87 81</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 84 87 81</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\examples\src\Executor.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 181</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 149 179</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 84 87 42 57 44 46 86 59</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 84 87 42 57 44 46 86 59</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\examples\src\Main.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 104</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 152 93</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 84 87 59 42 57 88 85 77 44 14 15 60 12 11 16 46 89 43 61 55 53 86 76 83 81 72</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 84 87 59 42 57 88 85 77 44 14 15 60 12 11 16 46 89 43 61 55 53 86 76 83 81 72</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\examples\src\IntrinsicsWrapper.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 116</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 190 115</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 59 10 22</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 59 10 22</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\examples\src\Model.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 113</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 105 148</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 57 84 87 88 77</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 57 84 87 88 77</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\examples\src\TemperatureCalculator.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 109</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 143 166</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 84 87 85 6 22 4 30 0 5 47 1</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 84 87 85 6 22 4 30 0 25 5 47 1</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\examples\src\TaskScheduler.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 156</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 188 127</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 84 87 88</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 84 87 88</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\examples\src\TimerInterruptConfigurator.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 184</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 153 132</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 84 87 61 55</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 84 87 61 55</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\examples\src\TemperatureFilter.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 124</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 141 128</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 84 87 77 6 22 4 30 0 5 47 1</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 84 87 77 6 22 4 30 0 25 5 47 1</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\examples\src\TimerConductor.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 125</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 175 170</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 84 87 46 53 89 55</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 84 87 46 53 89 55</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\examples\src\TimerConfigurator.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 98</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 147 186</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 84 87 43 61</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 84 87 43 61</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\examples\src\TimerHardware.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 155</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 95 167</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 84 87 89 43</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 84 87 89 43</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\examples\src\TimerInterruptHandler.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 142</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 151 174</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 84 87 55 61</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 84 87 55 61</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\examples\src\TimerModel.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 157</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 137 103</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 84 87 53 88</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 84 87 53 88</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\examples\src\UsartModel.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 145</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 150 168</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>ICCARM</name>
          <file> 84 87 12 82 11 77 24 22 30 0 25 5 47 1 33 6 4</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\examples\src\UsartBaudRateRegisterCalculator.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 119</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 172 163</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 84 87 11</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 84 87 11</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\examples\src\UsartConductor.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 177</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 146 138</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 84 87 44 14 12 88</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 84 87 44 14 12 88</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\examples\src\UsartConfigurator.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 178</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 169 139</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 84 87 15</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 84 87 15</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\examples\src\UsartHardware.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 136</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 99 107</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 84 87 14 15 60</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 84 87 14 15 60</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\examples\src\UsartTransmitBufferStatus.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 111</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 144 161</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>ICCARM</name>
          <file> 84 87 16</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\examples\src\UsartPutChar.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 118</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 140 108</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>ICCARM</name>
          <file> 84 87 60 16</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\srcIAR\Cstartup.s</name>
      <outputs>
        <tool>
          <name>AARM</name>
          <file> 97 135</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>AARM</name>
          <file> 222</file>
        </tool>
      </inputs>
    </file>
  </configuration>
  <configuration>
    <name>RAM_Debug</name>
    <outputs>
      <file>$PROJ_DIR$\Resource\SAM7_FLASH.mac</file>
      <file>$TOOLKIT_DIR$\inc\DLib_Defaults.h</file>
      <file>$TOOLKIT_DIR$\inc\DLib_Threads.h</file>
      <file>$PROJ_DIR$\..\..\test\system\src\TimerInterruptConfigurator.h</file>
      <file>$PROJ_DIR$\..\..\test\system\src\TimerModel.h</file>
      <file>$TOOLKIT_DIR$\inc\ymath.h</file>
      <file>$TOOLKIT_DIR$\inc\DLib_Product.h</file>
      <file>$TOOLKIT_DIR$\inc\math.h</file>
      <file>$PROJ_DIR$\..\..\test\system\src\TimerInterruptHandler.h</file>
      <file>$PROJ_DIR$\..\..\test\system\src\Executor.h</file>
      <file>$PROJ_DIR$\incIAR\project.h</file>
      <file>$TOOLKIT_DIR$\inc\intrinsics.h</file>
      <file>$PROJ_DIR$\..\..\examples\src\UsartBaudRateRegisterCalculator.h</file>
      <file>$PROJ_DIR$\..\..\examples\src\UsartModel.h</file>
      <file>$PROJ_DIR$\..\..\test\system\src\AT91SAM7X256.h</file>
      <file>$PROJ_DIR$\..\..\examples\src\UsartHardware.h</file>
      <file>$PROJ_DIR$\..\..\examples\src\UsartConfigurator.h</file>
      <file>$PROJ_DIR$\..\..\examples\src\UsartTransmitBufferStatus.h</file>
      <file>$PROJ_DIR$\..\..\test\system\src\UsartBaudRateRegisterCalculator.h</file>
      <file>$PROJ_DIR$\..\..\test\system\src\TemperatureCalculator.h</file>
      <file>$PROJ_DIR$\..\..\test\system\src\UsartPutChar.h</file>
      <file>$PROJ_DIR$\..\..\test\system\src\TaskScheduler.h</file>
      <file>$TOOLKIT_DIR$\lib\dl4t_tl_in.a</file>
      <file>$TOOLKIT_DIR$\inc\ycheck.h</file>
      <file>$PROJ_DIR$\..\..\test\system\src\ModelConfig.h</file>
      <file>$TOOLKIT_DIR$\inc\stdio.h</file>
      <file>$TOOLKIT_DIR$\inc\DLib_Config_Normal.h</file>
      <file>$PROJ_DIR$\..\..\test\system\src\UsartConductor.h</file>
      <file>$PROJ_DIR$\..\..\test\system\src\TemperatureFilter.h</file>
      <file>$PROJ_DIR$\..\..\test\system\src\Types.h</file>
      <file>$PROJ_DIR$\..\..\test\system\src\TimerConfigurator.h</file>
      <file>$TOOLKIT_DIR$\inc\yvals.h</file>
      <file>$PROJ_DIR$\..\..\test\system\src\UsartModel.h</file>
      <file>$TOOLKIT_DIR$\lib\rt4t_al.a</file>
      <file>$TOOLKIT_DIR$\inc\ysizet.h</file>
      <file>$PROJ_DIR$\..\..\test\system\src\AdcConductor.h</file>
      <file>$PROJ_DIR$\..\..\test\system\src\AdcTemperatureSensor.h</file>
      <file>$PROJ_DIR$\..\..\test\system\src\TimerHardware.h</file>
      <file>$PROJ_DIR$\..\..\test\system\src\AdcModel.h</file>
      <file>$PROJ_DIR$\..\..\test\system\src\AdcHardware.h</file>
      <file>$PROJ_DIR$\..\..\test\system\src\Model.h</file>
      <file>$TOOLKIT_DIR$\lib\shs_l.a</file>
      <file>$PROJ_DIR$\..\..\test\system\src\UsartConfigurator.h</file>
      <file>$PROJ_DIR$\..\..\examples\src\Executor.h</file>
      <file>$PROJ_DIR$\..\..\examples\src\TimerConfigurator.h</file>
      <file>$PROJ_DIR$\..\..\examples\src\UsartConductor.h</file>
      <file>$PROJ_DIR$\..\..\test\system\src\AdcTemperatureSensor.c</file>
      <file>$PROJ_DIR$\..\..\examples\src\TimerConductor.h</file>
      <file>$TOOLKIT_DIR$\inc\xencoding_limits.h</file>
      <file>$PROJ_DIR$\..\..\test\system\src\Main.c</file>
      <file>$PROJ_DIR$\..\..\test\system\src\AdcHardwareConfigurator.c</file>
      <file>$PROJ_DIR$\..\..\test\system\src\AdcConductor.c</file>
      <file>$PROJ_DIR$\..\..\test\system\src\AdcHardware.c</file>
      <file>$PROJ_DIR$\..\..\examples\src\TimerModel.h</file>
      <file>$PROJ_DIR$\..\..\test\system\src\AdcModel.c</file>
      <file>$PROJ_DIR$\..\..\examples\src\TimerInterruptHandler.h</file>
      <file>$PROJ_DIR$\..\..\test\system\src\Executor.c</file>
      <file>$PROJ_DIR$\..\..\examples\src\Model.h</file>
      <file>$PROJ_DIR$\..\..\test\system\src\Model.c</file>
      <file>$PROJ_DIR$\..\..\examples\src\IntrinsicsWrapper.h</file>
      <file>$PROJ_DIR$\..\..\examples\src\UsartPutChar.h</file>
      <file>$PROJ_DIR$\..\..\examples\src\TimerInterruptConfigurator.h</file>
      <file>$PROJ_DIR$\..\..\test\system\src\TemperatureFilter.c</file>
      <file>$PROJ_DIR$\..\..\test\system\src\TimerConfigurator.c</file>
      <file>$PROJ_DIR$\..\..\test\system\src\TemperatureCalculator.c</file>
      <file>$PROJ_DIR$\..\..\test\system\src\TaskScheduler.c</file>
      <file>$PROJ_DIR$\..\..\test\system\src\TimerInterruptConfigurator.c</file>
      <file>$PROJ_DIR$\..\..\test\system\src\TimerConductor.c</file>
      <file>$PROJ_DIR$\..\..\test\system\src\TimerInterruptHandler.c</file>
      <file>$PROJ_DIR$\..\..\test\system\src\UsartConfigurator.c</file>
      <file>$PROJ_DIR$\..\..\test\system\src\TimerHardware.c</file>
      <file>$PROJ_DIR$\..\..\test\system\src\UsartTransmitBufferStatus.c</file>
      <file>$PROJ_DIR$\..\..\examples\src\AdcModel.h</file>
      <file>$PROJ_DIR$\..\..\test\system\src\TimerModel.c</file>
      <file>$PROJ_DIR$\..\..\test\system\src\UsartBaudRateRegisterCalculator.c</file>
      <file>$PROJ_DIR$\..\..\test\system\src\UsartConductor.c</file>
      <file>$PROJ_DIR$\..\..\examples\src\AdcHardware.h</file>
      <file>$PROJ_DIR$\..\..\examples\src\TemperatureFilter.h</file>
      <file>$PROJ_DIR$\..\..\test\system\src\UsartHardware.c</file>
      <file>$PROJ_DIR$\..\..\test\system\src\UsartModel.c</file>
      <file>$PROJ_DIR$\..\..\test\system\src\UsartPutChar.c</file>
      <file>$PROJ_DIR$\..\..\examples\src\AdcTemperatureSensor.h</file>
      <file>$PROJ_DIR$\..\..\examples\src\ModelConfig.h</file>
      <file>$PROJ_DIR$\..\..\examples\src\AdcHardwareConfigurator.h</file>
      <file>$PROJ_DIR$\..\..\examples\src\Types.h</file>
      <file>$PROJ_DIR$\..\..\examples\src\TemperatureCalculator.h</file>
      <file>$PROJ_DIR$\..\..\examples\src\AdcConductor.h</file>
      <file>$PROJ_DIR$\..\..\examples\src\AT91SAM7X256.h</file>
      <file>$PROJ_DIR$\..\..\examples\src\TaskScheduler.h</file>
      <file>$PROJ_DIR$\..\..\examples\src\TimerHardware.h</file>
      <file>$PROJ_DIR$\..\..\test\system\src\TimerConductor.h</file>
      <file>$PROJ_DIR$\..\..\test\system\src\UsartTransmitBufferStatus.h</file>
      <file>$PROJ_DIR$\..\..\test\system\src\UsartHardware.h</file>
      <file>$PROJ_DIR$\..\..\test\system\src\AdcHardwareConfigurator.h</file>
      <file>$PROJ_DIR$\..\src\ext_irq.c</file>
      <file>$PROJ_DIR$\..\src\interrupt_timer.c</file>
      <file>$PROJ_DIR$\..\src\interrupt_Usart.c</file>
      <file>$PROJ_DIR$\..\src\main.c</file>
      <file>$PROJ_DIR$\RAM_Debug\Obj\UsartConductor.o</file>
      <file>$PROJ_DIR$\RAM_Debug\List\AdcConductor.lst</file>
      <file>$PROJ_DIR$\RAM_Debug\List\TimerInterruptHandler.lst</file>
      <file>$PROJ_DIR$\RAM_Debug\List\AdcHardwareConfigurator.lst</file>
      <file>$PROJ_DIR$\RAM_Debug\Obj\TimerHardware.pbi</file>
      <file>$PROJ_DIR$\RAM_Debug\Obj\UsartHardware.pbi</file>
      <file>$PROJ_DIR$\RAM_Debug\Obj\UsartConductor.pbi</file>
      <file>$PROJ_DIR$\RAM_Debug\List\TimerConfigurator.lst</file>
      <file>$PROJ_DIR$\RAM_Debug\List\UsartPutChar.lst</file>
      <file>$PROJ_DIR$\RAM_Debug\Obj\TimerConductor.o</file>
      <file>$PROJ_DIR$\RAM_Debug\Obj\TimerConfigurator.o</file>
      <file>$PROJ_DIR$\RAM_Debug\Obj\UsartBaudRateRegisterCalculator.pbi</file>
      <file>$PROJ_DIR$\RAM_Debug\Obj\AdcHardwareConfigurator.o</file>
      <file>$PROJ_DIR$\RAM_Debug\List\AdcModel.lst</file>
      <file>$PROJ_DIR$\RAM_Debug\List\TimerConductor.lst</file>
      <file>$PROJ_DIR$\RAM_Debug\List\TimerHardware.lst</file>
      <file>$PROJ_DIR$\RAM_Debug\Obj\AdcHardware.o</file>
      <file>$PROJ_DIR$\RAM_Debug\List\TemperatureFilter.lst</file>
      <file>$PROJ_DIR$\RAM_Debug\Obj\AdcModel.pbi</file>
      <file>$PROJ_DIR$\RAM_Debug\List\IntrinsicsWrapper.lst</file>
      <file>$PROJ_DIR$\RAM_Debug\Obj\Cstartup.o</file>
      <file>$PROJ_DIR$\RAM_Debug\Obj\interrupt_Usart.o</file>
      <file>$PROJ_DIR$\RAM_Debug\Obj\AdcHardwareConfigurator.pbi</file>
      <file>$PROJ_DIR$\RAM_Debug\Obj\main.pbi</file>
      <file>$PROJ_DIR$\RAM_Debug\List\AdcTemperatureSensor.lst</file>
      <file>$PROJ_DIR$\RAM_Debug\Obj\TimerInterruptHandler.pbi</file>
      <file>$PROJ_DIR$\RAM_Debug\List\Model.lst</file>
      <file>$PROJ_DIR$\RAM_Debug\Obj\Model.pbi</file>
      <file>$PROJ_DIR$\RAM_Debug\Obj\AdcHardware.pbi</file>
      <file>$PROJ_DIR$\RAM_Debug\List\UsartBaudRateRegisterCalculator.lst</file>
      <file>$PROJ_DIR$\RAM_Debug\Obj\ext_irq.o</file>
      <file>$PROJ_DIR$\RAM_Debug\Obj\TimerInterruptHandler.o</file>
      <file>$PROJ_DIR$\RAM_Debug\List\Cstartup.lst</file>
      <file>$PROJ_DIR$\RAM_Debug\Obj\TimerConfigurator.pbi</file>
      <file>$PROJ_DIR$\RAM_Debug\Obj\Cstartup_SAM7.o</file>
      <file>$PROJ_DIR$\RAM_Debug\Obj\AdcTemperatureSensor.pbi</file>
      <file>$PROJ_DIR$\RAM_Debug\List\UsartConfigurator.lst</file>
      <file>$PROJ_DIR$\RAM_Debug\List\UsartTransmitBufferStatus.lst</file>
      <file>$PROJ_DIR$\RAM_Debug\Obj\UsartPutChar.pbi</file>
      <file>$PROJ_DIR$\RAM_Debug\List\TaskScheduler.lst</file>
      <file>$PROJ_DIR$\RAM_Debug\Obj\interrupt_timer.pbi</file>
      <file>$PROJ_DIR$\RAM_Debug\List\UsartHardware.lst</file>
      <file>$PROJ_DIR$\RAM_Debug\Obj\TaskScheduler.pbi</file>
      <file>$PROJ_DIR$\RAM_Debug\Obj\Cstartup_SAM7.pbi</file>
      <file>$PROJ_DIR$\RAM_Debug\List\Cstartup_SAM7.lst</file>
      <file>$PROJ_DIR$\RAM_Debug\Obj\UsartTransmitBufferStatus.o</file>
      <file>$PROJ_DIR$\RAM_Debug\Obj\TimerModel.pbi</file>
      <file>$PROJ_DIR$\RAM_Debug\List\UsartConductor.lst</file>
      <file>$PROJ_DIR$\RAM_Debug\Obj\UsartPutChar.o</file>
      <file>$PROJ_DIR$\RAM_Debug\Obj\TimerInterruptConfigurator.pbi</file>
      <file>$PROJ_DIR$\RAM_Debug\Obj\TimerHardware.o</file>
      <file>$PROJ_DIR$\RAM_Debug\Obj\AdcTemperatureSensor.o</file>
      <file>$PROJ_DIR$\RAM_Debug\Obj\AdcModel.o</file>
      <file>$PROJ_DIR$\RAM_Debug\Obj\Executor.pbi</file>
      <file>$PROJ_DIR$\RAM_Debug\Obj\TemperatureFilter.o</file>
      <file>$PROJ_DIR$\RAM_Debug\Obj\AdcConductor.pbi</file>
      <file>$PROJ_DIR$\RAM_Debug\List\TimerInterruptConfigurator.lst</file>
      <file>$PROJ_DIR$\RAM_Debug\List\UsartModel.lst</file>
      <file>$PROJ_DIR$\RAM_Debug\Obj\UsartConfigurator.o</file>
      <file>$PROJ_DIR$\RAM_Debug\Obj\cmock_demo.pbd</file>
      <file>$PROJ_DIR$\RAM_Debug\Exe\cmock_demo.out</file>
      <file>$PROJ_DIR$\srcIAR\Cstartup_SAM7.c</file>
      <file>$PROJ_DIR$\RAM_Debug\List\TemperatureCalculator.lst</file>
      <file>$PROJ_DIR$\RAM_Debug\List\AdcHardware.lst</file>
      <file>$PROJ_DIR$\RAM_Debug\List\Executor.lst</file>
      <file>$PROJ_DIR$\RAM_Debug\Obj\UsartModel.pbi</file>
      <file>$PROJ_DIR$\RAM_Debug\Obj\TimerConductor.pbi</file>
      <file>$PROJ_DIR$\RAM_Debug\Obj\UsartConfigurator.pbi</file>
      <file>$PROJ_DIR$\RAM_Debug\Obj\UsartHardware.o</file>
      <file>$PROJ_DIR$\RAM_Debug\Obj\TemperatureCalculator.o</file>
      <file>$PROJ_DIR$\RAM_Debug\Obj\AdcConductor.o</file>
      <file>$PROJ_DIR$\RAM_Debug\Obj\IntrinsicsWrapper.o</file>
      <file>$PROJ_DIR$\RAM_Debug\Obj\Model.o</file>
      <file>$PROJ_DIR$\RAM_Debug\Obj\UsartTransmitBufferStatus.pbi</file>
      <file>$PROJ_DIR$\RAM_Debug\Obj\TimerModel.o</file>
      <file>$PROJ_DIR$\RAM_Debug\Obj\Executor.o</file>
      <file>$PROJ_DIR$\RAM_Debug\Obj\TemperatureCalculator.pbi</file>
      <file>$PROJ_DIR$\RAM_Debug\Obj\UsartBaudRateRegisterCalculator.o</file>
      <file>$PROJ_DIR$\RAM_Debug\Obj\TimerInterruptConfigurator.o</file>
      <file>$PROJ_DIR$\RAM_Debug\Obj\interrupt_Usart.pbi</file>
      <file>$PROJ_DIR$\RAM_Debug\Obj\TaskScheduler.o</file>
      <file>$PROJ_DIR$\RAM_Debug\Obj\Main.o</file>
      <file>$PROJ_DIR$\RAM_Debug\Obj\ext_irq.pbi</file>
      <file>$PROJ_DIR$\RAM_Debug\List\Main.lst</file>
      <file>$PROJ_DIR$\RAM_Debug\List\TimerModel.lst</file>
      <file>$PROJ_DIR$\RAM_Debug\Obj\interrupt_timer.o</file>
      <file>$PROJ_DIR$\RAM_Debug\Obj\UsartModel.o</file>
      <file>$PROJ_DIR$\RAM_Debug\Obj\TemperatureFilter.pbi</file>
      <file>$PROJ_DIR$\RAM_Debug\Obj\IntrinsicsWrapper.pbi</file>
      <file>$PROJ_DIR$\Resource\at91SAM7X256_RAM.icf</file>
      <file>$PROJ_DIR$\..\..\examples\src\AdcHardwareConfigurator.c</file>
      <file>$PROJ_DIR$\..\..\examples\src\AdcConductor.c</file>
      <file>$PROJ_DIR$\..\..\examples\src\AdcHardware.c</file>
      <file>$PROJ_DIR$\..\..\examples\src\AdcModel.c</file>
      <file>$PROJ_DIR$\..\..\examples\src\AdcTemperatureSensor.c</file>
      <file>$PROJ_DIR$\..\..\examples\src\Executor.c</file>
      <file>$PROJ_DIR$\..\..\examples\src\Main.c</file>
      <file>$PROJ_DIR$\..\..\examples\src\IntrinsicsWrapper.c</file>
      <file>$PROJ_DIR$\..\..\examples\src\Model.c</file>
      <file>$PROJ_DIR$\..\..\examples\src\TemperatureCalculator.c</file>
      <file>$PROJ_DIR$\Resource\SAM7_RAM.mac</file>
      <file>$PROJ_DIR$\Resource\at91SAM7X256_FLASH.icf</file>
      <file>$PROJ_DIR$\..\..\examples\src\TaskScheduler.c</file>
      <file>$PROJ_DIR$\..\..\examples\src\TimerInterruptConfigurator.c</file>
      <file>$PROJ_DIR$\..\..\examples\src\TemperatureFilter.c</file>
      <file>$PROJ_DIR$\..\..\examples\src\TimerConductor.c</file>
      <file>$PROJ_DIR$\..\..\examples\src\TimerConfigurator.c</file>
      <file>$PROJ_DIR$\..\..\examples\src\TimerHardware.c</file>
      <file>$PROJ_DIR$\..\..\examples\src\TimerInterruptHandler.c</file>
      <file>$PROJ_DIR$\..\..\examples\src\TimerModel.c</file>
      <file>$PROJ_DIR$\..\..\examples\src\UsartModel.c</file>
      <file>$PROJ_DIR$\..\..\examples\src\UsartBaudRateRegisterCalculator.c</file>
      <file>$PROJ_DIR$\..\..\examples\src\UsartConductor.c</file>
      <file>$PROJ_DIR$\..\..\examples\src\UsartConfigurator.c</file>
      <file>$PROJ_DIR$\..\..\examples\src\UsartHardware.c</file>
      <file>$PROJ_DIR$\..\..\examples\src\UsartTransmitBufferStatus.c</file>
      <file>$PROJ_DIR$\..\..\examples\src\UsartPutChar.c</file>
      <file>$PROJ_DIR$\srcIAR\Cstartup.s</file>
      <file>$PROJ_DIR$\incIAR\AT91SAM7X-EK.h</file>
      <file>$PROJ_DIR$\incIAR\lib_AT91SAM7X256.h</file>
      <file>$PROJ_DIR$\incIAR\AT91SAM7X256_inc.h</file>
    </outputs>
    <file>
      <name>[ROOT_NODE]</name>
      <outputs>
        <tool>
          <name>ILINK</name>
          <file> 158</file>
        </tool>
      </outputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\test\system\src\AdcTemperatureSensor.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 133</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 122 149</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 29 14 36</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 29 14 36</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\test\system\src\Main.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 121</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 181 179</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 29 14 9 40 21 19 28 27 92 42 20 32 18 91 90 37 30 3 8 4 35 39 93 36 38</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 29 14 9 40 21 19 28 27 92 42 20 32 18 91 90 37 30 3 8 4 35 39 93 36 38</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\test\system\src\AdcHardwareConfigurator.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 120</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 101 110</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 29 14 93 24</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 29 14 93 24</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\test\system\src\AdcConductor.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 153</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 99 168</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 29 14 35 38 39</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 29 14 35 38 39</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\test\system\src\AdcHardware.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 126</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 161 114</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 29 14 39 93 36</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 29 14 39 93 36</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\test\system\src\AdcModel.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 116</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 111 150</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 29 14 38 21 19 28</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 29 14 38 21 19 28</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\test\system\src\Executor.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 151</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 162 173</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 29 14 9 40 27 90 35 11 23</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 29 14 9 40 27 90 35 11 23</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\test\system\src\Model.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 125</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 124 170</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 40 29 14 21 28</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 40 29 14 21 28</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\test\system\src\TemperatureFilter.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 185</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 115 152</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 29 14 28 7 23 5 31 1 6 48 2</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 29 14 28 7 23 5 31 1 26 6 48 2</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\test\system\src\TimerConfigurator.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 131</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 105 108</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 29 14 30 3</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 29 14 30 3</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\test\system\src\TemperatureCalculator.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 174</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 160 167</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 29 14 19 7 23 5 31 1 6 48 2</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 29 14 19 7 23 5 31 1 26 6 48 2</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\test\system\src\TaskScheduler.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 140</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 137 178</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 29 14 21</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 29 14 21</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\test\system\src\TimerInterruptConfigurator.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 147</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 154 176</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 29 14 3 8</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 29 14 3 8</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\test\system\src\TimerConductor.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 164</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 112 107</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 29 14 90 4 37 8</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 29 14 90 4 37 8</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\test\system\src\TimerInterruptHandler.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 123</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 100 129</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 29 14 8 3</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 29 14 8 3</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\test\system\src\UsartConfigurator.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 165</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 134 156</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 29 14 42</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 29 14 42</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\test\system\src\TimerHardware.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 102</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 113 148</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 29 14 37 30</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 29 14 37 30</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\test\system\src\UsartTransmitBufferStatus.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 171</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 135 143</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 29 14 91</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 29 14 91</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\test\system\src\TimerModel.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 144</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 182 172</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 29 14 4 21</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 29 14 4 21</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\test\system\src\UsartBaudRateRegisterCalculator.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 109</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 127 175</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 29 14 18</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 29 14 18</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\test\system\src\UsartConductor.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 104</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 145 98</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 29 14 27 92 32 21</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 29 14 27 92 32 21</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\test\system\src\UsartHardware.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 103</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 139 166</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 29 14 92 42 20</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 29 14 92 42 20</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\test\system\src\UsartModel.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 163</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 155 184</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 29 14 32 24 18 28 25 23 31 1 6 48 2 34 7 5</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 29 14 32 24 18 28 25 23 31 1 26 6 48 2 34 7 5</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\test\system\src\UsartPutChar.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 136</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 106 146</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 29 14 20 91</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 29 14 20 91</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\src\ext_irq.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 180</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 128</file>
        </tool>
      </outputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\src\interrupt_timer.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 138</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 183</file>
        </tool>
      </outputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\src\interrupt_Usart.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 177</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 119</file>
        </tool>
      </outputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\src\main.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 121</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 179</file>
        </tool>
      </outputs>
    </file>
    <file>
      <name>$PROJ_DIR$\RAM_Debug\Obj\cmock_demo.pbd</name>
      <inputs>
        <tool>
          <name>BILINK</name>
          <file> 153 126 120 116 133 141 151 186 125 140 174 185 164 131 102 147 123 144 109 104 165 103 163 136 171 121</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\RAM_Debug\Exe\cmock_demo.out</name>
      <inputs>
        <tool>
          <name>ILINK</name>
          <file> 187 168 114 110 150 149 118 132 173 169 179 170 178 167 152 107 108 148 176 129 172 175 98 156 166 184 146 143 41 33 22</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\srcIAR\Cstartup_SAM7.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 141</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 142 132</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 10 11 23 216 87 217</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 10 11 23 216 87 217</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\examples\src\AdcHardwareConfigurator.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 120</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 101 110</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 84 87 83 82</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 84 87 83 82</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\examples\src\AdcConductor.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 153</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 99 168</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 84 87 86 72 76</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 84 87 86 72 76</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\examples\src\AdcHardware.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 126</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 161 114</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 84 87 76 83 81</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 84 87 76 83 81</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\examples\src\AdcModel.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 116</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 111 150</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 84 87 72 88 85 77</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 84 87 72 88 85 77</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\examples\src\AdcTemperatureSensor.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 133</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 122 149</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 84 87 81</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 84 87 81</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\examples\src\Executor.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 151</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 162 173</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 84 87 43 57 45 47 86 59</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 84 87 43 57 45 47 86 59</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\examples\src\Main.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 121</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 181 179</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 84 87 59 43 57 88 85 77 45 15 16 60 13 12 17 47 89 44 61 55 53 86 76 83 81 72</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 84 87 59 43 57 88 85 77 45 15 16 60 13 12 17 47 89 44 61 55 53 86 76 83 81 72</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\examples\src\IntrinsicsWrapper.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 186</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 117 169</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 59 11 23</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 59 11 23</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\examples\src\Model.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 125</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 124 170</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 57 84 87 88 77</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 57 84 87 88 77</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\examples\src\TemperatureCalculator.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 174</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 160 167</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 84 87 85 7 23 5 31 1 6 48 2</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 84 87 85 7 23 5 31 1 26 6 48 2</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\examples\src\TaskScheduler.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 140</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 137 178</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 84 87 88</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 84 87 88</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\examples\src\TimerInterruptConfigurator.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 147</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 154 176</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 84 87 61 55</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 84 87 61 55</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\examples\src\TemperatureFilter.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 185</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 115 152</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 84 87 77 7 23 5 31 1 6 48 2</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 84 87 77 7 23 5 31 1 26 6 48 2</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\examples\src\TimerConductor.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 164</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 112 107</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 84 87 47 53 89 55</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 84 87 47 53 89 55</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\examples\src\TimerConfigurator.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 131</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 105 108</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 84 87 44 61</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 84 87 44 61</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\examples\src\TimerHardware.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 102</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 113 148</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 84 87 89 44</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 84 87 89 44</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\examples\src\TimerInterruptHandler.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 123</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 100 129</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 84 87 55 61</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 84 87 55 61</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\examples\src\TimerModel.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 144</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 182 172</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 84 87 53 88</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 84 87 53 88</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\examples\src\UsartModel.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 163</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 155 184</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 84 87 13 82 12 77 25 23 31 1 6 48 2 34 7 5</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 84 87 13 82 12 77 25 23 31 1 26 6 48 2 34 7 5</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\examples\src\UsartBaudRateRegisterCalculator.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 109</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 127 175</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 84 87 12</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 84 87 12</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\examples\src\UsartConductor.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 104</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 145 98</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 84 87 45 15 13 88</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 84 87 45 15 13 88</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\examples\src\UsartConfigurator.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 165</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 134 156</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 84 87 16</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 84 87 16</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\examples\src\UsartHardware.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 103</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 139 166</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 84 87 15 16 60</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 84 87 15 16 60</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\examples\src\UsartTransmitBufferStatus.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 171</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 135 143</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 84 87 17</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 84 87 17</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\..\..\examples\src\UsartPutChar.c</name>
      <outputs>
        <tool>
          <name>BICOMP</name>
          <file> 136</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 106 146</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>BICOMP</name>
          <file> 84 87 60 17</file>
        </tool>
        <tool>
          <name>ICCARM</name>
          <file> 84 87 60 17</file>
        </tool>
      </inputs>
    </file>
    <file>
      <name>$PROJ_DIR$\srcIAR\Cstartup.s</name>
      <outputs>
        <tool>
          <name>AARM</name>
          <file> 118 130</file>
        </tool>
      </outputs>
      <inputs>
        <tool>
          <name>AARM</name>
          <file> 218</file>
        </tool>
      </inputs>
    </file>
    <forcedrebuild>
      <name>$PROJ_DIR$\..\src\ext_irq.c</name>
      <tool>ICCARM</tool>
    </forcedrebuild>
    <forcedrebuild>
      <name>$PROJ_DIR$\..\src\interrupt_timer.c</name>
      <tool>ICCARM</tool>
    </forcedrebuild>
    <forcedrebuild>
      <name>$PROJ_DIR$\..\src\interrupt_Usart.c</name>
      <tool>ICCARM</tool>
    </forcedrebuild>
    <forcedrebuild>
      <name>$PROJ_DIR$\..\src\main.c</name>
      <tool>ICCARM</tool>
    </forcedrebuild>
  </configuration>
</project>


