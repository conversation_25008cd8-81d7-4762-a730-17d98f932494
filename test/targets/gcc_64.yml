---
compiler:
  path: gcc
  source_path:     &systest_generated_path './system/generated/'
  unit_tests_path: &unit_tests_path '../examples/test/'
  mocks_path:      &systest_mocks_path './system/generated/'
  build_path:      &systest_build_path './system/build/'
  options:
    - '-c'
    - '-m64'
    - '-Wall'
    - '-Wunused-parameter'
    - '-Wno-address'
    - '-Wno-invalid-token-paste'
    - '-std=c99'
    - '-pedantic'
  includes:
    prefix: '-I'
    items:
      - *systest_generated_path
      - *unit_tests_path
      - *systest_mocks_path
      - '../src/'
      - '../vendor/unity/src/'
      - '../vendor/c_exception/lib/'
      - './system/test_compilation/'
      - './'
  defines:
    prefix: '-D'
    items:
      - CMOCK
      - 'UNITY_SUPPORT_64'
      - 'UNITY_POINTER_WIDTH=64'
  object_files:
    prefix: '-o'
    extension: '.o'
    destination: *systest_build_path

linker:
  path: gcc
  options:
    - -lm
    - '-m64'
  includes:
    prefix: '-I'
  object_files:
    path: *systest_build_path
    extension: '.o'
  bin_files:
    prefix: '-o'
    extension: '.exe'
    destination: *systest_build_path

unsupported:
  - out_of_memory
  - callingconv

colour: true
