---
:cmock:
  :plugins:
  - # none
  :skeleton_path: system/generated

:systest:
  :types: |
    #define UINT32 unsigned int

  :mockable: |
    UINT32 something(int a);

  :skeleton: skeleton_update.h

  :source:
    :header: |
      void function_a(void);
      int function_b(int a, int b);
      const char* function_c(void);

    # note that this code section exists and will be updated by the skeleton generator
    :code: |
      const char* donuts = "donuts";
      const char* function_c(void)
      {
        return donuts;
      }

      int function_d(void)
      {
        return 77;
      }


  :tests:
    :common: |
      void setUp(void) {}
      void tearDown(void) {}
      extern int function_d();

    :units:
    - :pass: TRUE
      :should: 'generate an empty shell for functions with no return values'
      :code: |
        test()
        {
          function_a();
        }

    - :pass: TRUE
      :should: 'return numerical zero for numerical return values'
      :code: |
        test()
        {
          TEST_ASSERT_EQUAL_INT(0, function_b(1, 2));
        }

    - :pass: TRUE
      :should: 'not overwrite functions that already exist'
      :code: |
        test()
        {
          TEST_ASSERT_EQUAL_STRING("donuts", function_c());
        }

    - :pass: TRUE
      :should: 'leave functions it has never heard of'
      :code: |
        test()
        {
          TEST_ASSERT_EQUAL_INT(77, function_d());
        }


...
